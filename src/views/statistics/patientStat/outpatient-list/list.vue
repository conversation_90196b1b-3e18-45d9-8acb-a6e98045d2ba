<template>
    <abc-layout class="common-padding-container" preset="page-table">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :enable-features="toolbarFeatures"
                :patient-id-filter.sync="params.patientId"
                :clinic-id-filter.sync="params.clinicId"
                :doctor-id-filter.sync="params.employeeId"
                :date-filter.sync="params.dateFilter$"
                :handle-export="exports"
                :setting-options="settingOptions"
                :export-task-type="exportTaskType"
                :clinic-list="clinicList"
                :doctor-list="doctorList"
                :custom-clinic-employee="true"
                doctor-id-with-name
                @change-date="handleDateChange"
                @change-clinic="changeClinicEmployee"
                @change-doctor="changeClinicEmployee"
                @setting-item-click="handleOpenCustomHeaderDialog"
                @change-patient="
                    () => {
                        queryPatientList(true);
                    }
                "
            >
                <filter-select
                    v-model="params.departmentId"
                    clearable
                    :width="120"
                    placeholder="科室"
                    :options="departmentList"
                    @change="queryPatientList"
                >
                </filter-select>
                <abc-checkbox
                    v-model="isInfectiousDiseases"
                    type="number"
                >
                    仅看传染病患者
                </abc-checkbox>
            </stat-toolbar>
        </abc-layout-header>

        <!--表格-->
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :key="tableFixed2Key"
                :loading="loading"
                :data-list="tableList"
                :render-config="renderTableHeader"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="currentPageParams"
                :count="tableCount"
                :class="{ 'show-total': true }"
                @current-change="changePageIndex"
            >
                <ul slot="tipsContent">
                    <li>
                        共 <span>{{ tableCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import { mapGetters } from 'vuex';
    import StatAPI from 'api/stat';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import {
        formatAge, isNotNull, tryJSONParse,
    } from 'utils/index';
    import {
        formatDate, parseTime,
    } from '@abc/utils-date';
    import { freq2Han } from '@/filters';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import { AstEnum } from 'views/layout/prescription/constant.js';
    import infectiousDiseases from 'assets/configure/infectious-diseases.js';
    import { resolveHeaderV2 } from 'src/views/statistics/utils.js';
    import {
        outpatientLogRenderTypeList,
        outpatientLogRenderTypeMap,
        outpatientLogStaticConfig,
    } from 'src/views/statistics/patientStat/outpatient-list/config.js';
    import { GlassesMaterialTypeEnum } from 'src/views/layout/prescription/constant';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import SettingAPI from 'api/settings';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';
    import SignService from 'utils/sign-service';

    export default {
        name: 'OutpatientList',
        components: {
            FilterSelect,
            StatToolbar,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins],
        data() {
            return {
                params: {
                    clinicId: '',
                    employeeId: '',
                    patientId: '',
                    pageIndex: 0,
                    pageSize: 0,
                    sortConfig: {
                        orderBy: '',
                        orderType: '',
                    },
                    isInfectiousDiseases: 0,
                    departmentId: '',
                },
                loading: false,
                tableCount: 0,
                tableList: [],
                postData: {
                    patientName: '',
                    patientId: '',
                },
                selectedDoctor: '',
                doctorKey: '',
                currentDoctors: [],
                clinicEmployees: [],
                tableHeader: [],
                exportTaskType: 'outpatient-list',
                settingOptions: [
                    {
                        text: '设置展示字段',
                        value: '',
                        isOpen: false,
                        groupName: '',
                    },
                ],
                // 给 tableFixed2 组件添加 key,每次数据更新时同时更新该 key,以便重新渲染 tableFixed2 组件
                // 为了解决自定义渲染时,格子内容出现残留的问题
                tableFixed2Key: '-1',
                departmentList: [],
            };
        },

        computed: {
            ...mapGetters(['enablePatientMobileInStatistics','isForbiddenExport']),
            ...mapGetters('theme', ['curTypeId']),

            renderTableHeader() {
                const config = resolveHeaderV2({
                    header: this.tableHeader,
                    staticConfig: outpatientLogStaticConfig,
                    renderTypeList: outpatientLogRenderTypeList,
                    renderTypeMap: outpatientLogRenderTypeMap,
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    ...config,
                };
            },
            currentPageParams() {
                return {
                    showTotalPage: false,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.pageSize,
                    count: this.tableCount,
                };
            },

            toolbarFeatures() {
                const features = [
                    StatToolbar.Feature.DATE,
                    StatToolbar.Feature.CLINIC,
                    StatToolbar.Feature.DOCTOR,
                    StatToolbar.Feature.PATIENT,
                    StatToolbar.Feature.SETTING,
                ];
                return this.isForbiddenExport ? features : [
                    StatToolbar.Feature.EXPORT,
                    ...features,
                ];
            },

            clinicList() {
                return this.subClinics.map((clinic) => {
                    return {
                        ...clinic,
                        shortName: this.chainId === clinic.id ? '总部' : clinic.shortName,
                    };
                });
            },

            tableKey() {
                const {
                    outpatientLog: {
                        showOralExamination, showOphthalmology,
                    },
                } = this.viewDistributeConfig.Statistics;
                if (showOralExamination) return 'stat.oral.outpatient.list';
                if (showOphthalmology) return 'stat.eye.outpatient.list';
                return 'stat.outpatient.list';
            },

            doctorList() {
                let currentClinicId = this.params.clinicId;
                if (this.isSingleStore || this.isChainSubStore) {
                    currentClinicId = this.clinicId;
                }

                if (currentClinicId) {
                    return this.clinicEmployees;
                }
                return [];
            },

            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            isInfectiousDiseases: {
                get() {
                    return this.params.isInfectiousDiseases;
                },
                set(value) {
                    this.params.isInfectiousDiseases = value;
                    this.queryPatientList();
                },
            },
        },
        watch: {
            'params.clinicId': {
                handler(clinicIdFilterNew) {
                    // 切换门店查询
                    if (clinicIdFilterNew) {
                        this.fetchClinicEmployees(clinicIdFilterNew);
                    }
                },
            },
            'params.dateFilter$': {
                handler() {
                    this.fetchClinicEmployees(this.params.clinicId || '');
                },
            },
        },

        mounted() {
            const date = formatDate(new Date());
            this.params.clinicId = '';
            this.params.dateFilter = {
                begin: date,
                end: date,
                dateRange: [date, date],
            };
            this.$abcEventBus.$on('amount-setting-success', () => this.fetchList(), this);
            this.fetchClinicEmployees('');
            this.fetchClinicDepartments();
            this.exportService = new ExportService();
        },
        beforeDestroy() {
            this.exportService.destroy();
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            async handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                await this.fetchList();
            },
            handleOpenCustomHeaderDialog() {
                new BizCustomHeader({
                    value: true,
                    tableKey: this.tableKey,
                    titleName: '运营分析 - 门诊日志',
                    mode: 'draggle',
                    finishFunc: this.fetchList,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },
            formatPrescriptionOneLine(item) {
                if (
                    item.prescriptionChineseForms && item.prescriptionChineseForms.length === 0 &&
                    item.prescriptionWesternForms && item.prescriptionWesternForms.length === 0 &&
                    item.prescriptionInfusionForms && item.prescriptionInfusionForms.length === 0 &&
                    item.prescriptionExternalTreatForms && item.prescriptionExternalTreatForms.length === 0 &&
                    !item?.prescriptionGlassesForms
                ) {
                    return '-';
                }
                let res = '';
                item.prescriptionWesternForms && item.prescriptionWesternForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((formItem) => {
                        res += `${formItem.name} ${formItem.usage}${formItem.ast === AstEnum.PI_SHI ? '(皮试)' : ''}，${
                            freq2Han(formItem.freq) ? `${freq2Han(formItem.freq)}，` : ''
                        }每次${formItem.dosage}${formItem.dosage_unit} ${formItem.unit_count}${formItem.unit}`;
                    });
                });

                item.prescriptionInfusionForms && item.prescriptionInfusionForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((formItem) => {
                        res += `${formItem.name} ${formItem.usage}${formItem.ast === AstEnum.PI_SHI ? '(皮试)' : ''}，${
                            freq2Han(formItem.freq) ? `${freq2Han(formItem.freq)}，` : ''
                        }每次${formItem.dosage}${formItem.dosage_unit} ${formItem.unit_count}${formItem.unit}`;
                    });
                });

                item.prescriptionChineseForms && item.prescriptionChineseForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((formItem) => {
                        res += `${formItem.name} ${formItem.unit_count}${formItem.unit || 'g'}${
                            formItem.special_requirement ? `(${formItem.special_requirement})` : ''
                        }\n`;
                    });
                });

                item.prescriptionExternalTreatForms && item.prescriptionExternalTreatForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((formItem) => {
                        res += `${formItem.name} ${formItem.unit_count}${formItem.unit}；`;
                    });
                });


                const glassesForms = item?.prescriptionGlassesForms?.glassesParams ? this.formatGlassHeader(item?.prescriptionGlassesForms, true) : '';

                glassesForms?.glassesParams?.filter((it) => it.rightEyeValue)?.forEach((formItem, index) => {
                    res += `${index === 0 ? '右眼：' : ''}${formItem.name}${formItem.rightEyeValue}${formItem.unit}，`;
                });
                glassesForms?.glassesParams?.filter((it) => it.leftEyeValue)?.forEach((formItem, index) => {
                    res += `${index === 0 ? '左眼：' : ''}${formItem.name}${formItem.leftEyeValue}${formItem.unit}`;
                });

                return res;
            },
            changePatientHandler(val) {
                this.params.patientId = val.patientId || '';
                this.fetchList();
            },
            formatGlassHeader(prescriptionGlassesForms, isSummary = false) {
                if (!prescriptionGlassesForms) return null;
                const {
                    glassesParams, glassesType,
                } = prescriptionGlassesForms || {};
                const allList = [
                    {
                        name: '球镜',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameSpherical',
                        unit: 'D',
                    },
                    {
                        name: '柱镜',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameLenticular',
                        unit: 'D',
                    },
                    {
                        name: '轴位',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameAxial',
                        unit: '°',
                    },
                    {
                        name: '棱镜',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'framePrism',
                        unit: '△',
                    },
                    {
                        name: '基底',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameBase',
                        unit: '',
                    },
                    {
                        name: '矫正视力',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameCva',
                        unit: '',
                    },
                    {
                        name: '下加光',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'frameAdd',
                        unit: 'D',
                    },
                    {
                        name: '瞳距',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'framePupilDistance',
                        unit: 'mm',
                    },
                    {
                        name: '瞳高',
                        glassesType: GlassesMaterialTypeEnum.FRAME,
                        key: 'framePupilHeight',
                        unit: 'mm',
                    },
                    {
                        name: '后顶焦度',
                        glassesType: GlassesMaterialTypeEnum.CONTACT,
                        key: 'contactFocalLength',
                        unit: 'D',
                    },
                    {
                        name: '基弧',
                        glassesType: GlassesMaterialTypeEnum.CONTACT,
                        key: 'contactBozr',
                        unit: 'mm',
                    },
                    {
                        name: '直径',
                        glassesType: GlassesMaterialTypeEnum.CONTACT,
                        key: 'contactDiameter',
                        unit: 'mm',
                    },
                    {
                        name: '柱镜',
                        glassesType: GlassesMaterialTypeEnum.CONTACT,
                        key: 'contactLenticular',
                        unit: 'D',
                    },
                    {
                        name: '轴位',
                        glassesType: GlassesMaterialTypeEnum.CONTACT,
                        key: 'contactAxial',
                        unit: '°',
                    },
                ];
                const validList = allList.filter((item) => item.glassesType === +glassesType);
                const glassesParamsList = [];
                glassesParams?.forEach((item) => {
                    const {
                        name, leftEyeValue, rightEyeValue, key,
                    } = item;
                    validList.forEach((ele) => {
                        if (ele.name === name && ele.key === key) {
                            glassesParamsList.push({
                                name,
                                leftEyeValue,
                                rightEyeValue,
                                unit: ele.unit,
                                glassesType: ele.glassesType,
                            });
                        }
                    });
                });

                if (!isSummary) {
                    glassesParamsList.unshift({
                        name: prescriptionGlassesForms?.usage || '',
                        leftEyeValue: '左眼',
                        rightEyeValue: '右眼',
                        glassesType,
                    });
                }

                return {
                    optometristName: prescriptionGlassesForms?.optometristName || '-',
                    requirement: prescriptionGlassesForms?.requirement || '-',
                    glassesParams: glassesParamsList,
                    glassesType,
                };
            },
            async fetchList() {
                await this.$nextTick();
                this.loading = true;
                const {
                    pageIndex, pageSize, dateFilter$, patientId, clinicId, employeeId, isInfectiousDiseases,departmentId,
                } = this.params;
                const {
                    begin: beginDate, end: endDate,
                } = dateFilter$;
                const employees = employeeId ? [
                    {
                        id: employeeId.split('-idWithName-')[0],
                        name: employeeId.split('-idWithName-')[1],
                    },
                ] : [];
                try {
                    const res = await StatAPI.getOutPatientList(
                        {
                            pageIndex,
                            pageSize,
                            beginDate,
                            endDate,
                            clinicId,
                            employees: JSON.stringify(employees),
                            patientId,
                            isInfectiousDiseases,
                            departmentId,
                            enablePatientMobile: this.enablePatientMobileInStatistics,
                            e: 1, //需要加密
                        },
                    );
                    await this.handleTableList(res?.data?.list ?? []);
                    this.tableCount = res.data.count;
                    this.tableHeader = res.data.header;
                    this.tableFixed2Key = Math.random().toString(); // 每次请求数据都生成新的 key,用于刷新表格

                    /**
                     * 自定义渲染[诊断]列
                     */
                    const diagnosisIndex = this.tableHeader.findIndex((item) => item.label === '诊断');
                    if (diagnosisIndex !== -1) {
                        this.tableHeader[diagnosisIndex].render = (h, row) => {
                            return this.getDiagnosticColumnRender(row);
                        };
                    }
                } catch (e) {
                    console.error('fetchList error', e);
                } finally {
                    this.loading = false;
                }
            },

            async handleTableList(list = []) {
                // if (!list || list.length === 0) {
                //     this.tableList = [];
                //     return;
                // }
                // const decryptKeys = [
                //     'patientName', 'patientSn', 'idNumber', 'patientMobile', 'address', 'company', 'diagnosis', 'chiefComplaint', 'pastHistory','prescriptionWesternForms','prescriptionExternalTreatForms','prescriptionChineseForms','prescriptionInfusionForms','prescriptionGlassesForms',
                // ];
                // // 处理所有需要解密的字段
                // const decryptAllFields = async () => {
                //     const decryptedData = {};
                //
                //     // 为每个需要解密的字段创建解密 Promise
                //     for (const key of decryptKeys) {
                //         const decryptPromises = list.map((item) => {
                //             // 检查字段是否存在且有值
                //             if (isNotNull(item[key])) {
                //                 return SignService.aecSignForDec(item[key], this.curTypeId);
                //             }
                //             // 如果字段不存在或为空，返回原值或空字符串
                //             return Promise.resolve(item[key] || '');
                //         }) || [];
                //
                //         // 等待当前字段的所有解密操作完成
                //         decryptedData[key] = await Promise.all(decryptPromises);
                //     }
                //
                //     return decryptedData;
                // };
                //
                // // 等待所有字段的解密操作完成
                // const allDecryptedData = await decryptAllFields();
                //
                // this.tableList = list.map((item, index) => {
                //     // 为当前项应用所有解密后的数据
                //     const decryptedFields = {};
                //     decryptKeys.forEach((key) => {
                //         if (allDecryptedData[key] && allDecryptedData[key][index] !== undefined) {
                //             decryptedFields[key] = tryJSONParse(allDecryptedData[key][index]);
                //         }
                //     });
                //     const base = {
                //         ...item,
                //         ...decryptedFields,
                //     };
                //
                //     return {
                //         ...base,
                //         age: formatAge(base.patientAge),
                //         created: parseTime(base.created, 'y-m-d h:i:s', true) || '-',
                //         clinicName: base.shortName || base.clinicName || '-',
                //         patientMobile: base.patientMobile || '-',
                //         diagnoseStatus: base.diagnoseStatus || '-',
                //         sourceFrom: base.sourceFrom || '-',
                //         referrer: base.referrer || '-',
                //         chiefComplaint: base.chiefComplaint || '-',
                //         presentHistory: base.presentHistory || '-',
                //         pastHistory: base.pastHistory || '-',
                //         physicalExamination: base.physicalExamination || '-',
                //         oralExamination: base.oralExamination || '-',
                //         auxiliaryExaminations: base.auxiliaryExaminations || '-',
                //         chineseExamination: base.chineseExamination || '-',
                //         diagnosis: base.diagnosis || '-',
                //         syndrome: base.syndrome || '-',
                //         therapy: base.therapy || '-',
                //         doctorAdvice: base.doctorAdvice || '-',
                //         prescriptionSummary: this.formatPrescriptionOneLine(base),
                //         prescriptionGlassesForms: this.formatGlassHeader(base?.prescriptionGlassesForms),
                //     };
                // }) || [];
            },

            handleDateChange() {
                this.queryPatientList();
            },
            // 分页
            changePageIndex(index) {
                this.params.pageIndex = index - 1;
                this.queryPatientList(false);
            },

            queryPatientList(resetPageParams = true) {
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                this.fetchList();
            },

            changeClinicEmployee() {
                this.queryPatientList();
            },

            // 导出患者清单
            async exports() {
                const {
                    dateFilter$, patientId, clinicId, employeeId, isInfectiousDiseases,
                } = this.params;
                const {
                    begin: beginDate, end: endDate,
                } = dateFilter$;
                const employees = employeeId ? [
                    {
                        id: employeeId.split('-idWithName-')[0],
                        name: employeeId.split('-idWithName-')[1],
                    },
                ] : [];
                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        beginDate,
                        endDate,
                        pageIndex: 0,
                        pageSize: 0,
                        clinicId,
                        patientId,
                        employees,
                        isInfectiousDiseases,
                        enablePatientMobile: this.enablePatientMobileInStatistics,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            async fetchClinicEmployees(id) {
                const {
                    dateFilter$,
                } = this.params;
                const {
                    begin: beginDate, end: endDate,
                } = dateFilter$;
                const params = {
                    beginDate,
                    endDate,
                    scope: 1,
                };
                if (id) {
                    params.clinicid = id;
                }
                const { data } = await StatAPI.fetchOutpatientEmployees(params);
                this.clinicEmployees = data || [];
            },

            async fetchClinicDepartments() {
                try {
                    const { data } = await SettingAPI.clinic.fetchClinicDepartments({
                        showDisable: 1,
                    });
                    this.departmentList = data.data.rows ?? [];
                } catch (e) {
                    console.log(e);
                    this.departmentList = [];
                }
            },

            /**
             * 表格[诊断]列的自定义渲染函数
             */
            getDiagnosticColumnRender(data) {
                let isShowInfectiousDiseaseWarn = false; // 是否显示传染病警告
                const result = {
                    classA: [], classB: [], classC: [],
                }; // 诊断中的传染病列表
                const diagnosisList = data.diagnosis.trim().split('，'); // 诊断中的疾病列表

                const infectedDiseaseGrade = Object.keys(infectiousDiseases);
                infectedDiseaseGrade.forEach((item) => {
                    infectiousDiseases[item].forEach((ele) => {
                        const resDiagnosis = diagnosisList.find((diagnosis) => diagnosis === ele);
                        if (resDiagnosis) {
                            isShowInfectiousDiseaseWarn = true;
                            result[item].push(resDiagnosis);
                        }
                    });
                });
                // title指令
                const directives = [
                    {
                        name: 'abc-title',
                        value: data.diagnosis.trim(),
                        modifiers: {
                            ellipsis: true,
                        },
                    },
                ];
                if (isShowInfectiousDiseaseWarn) {
                    /**
                     * 替换等级文本
                     */
                    const gradeFilter = (val) => {
                        if (val === 'classA') return '甲类';
                        if (val === 'classB') return '乙类';
                        return '丙类';
                    };
                    return (
                        <div style={{
 display: 'flex', 'align-items': 'center', padding: '0px 10px',
}}>
                            <abc-popover
                                trigger="hover"
                                theme="yellow"
                                placeholder="传染病"
                            >
                                <abc-icon
                                    slot="reference"
                                    icon="Attention"
                                    color="$Y2"
                                    size="16"
                                    style={{
                                        display: 'flex',
                                        'align-items': 'center',
                                        'justify-content': 'center',
                                    }}
                                ></abc-icon>
                                <div class="infectious-disease-popover-wrapper">
                                    <div style={{ 'margin-bottom': '5px' }} class="popover-title-color">诊断中含有三类传染病诊断</div>
                                    {
                                        Object.keys(result).map((item) => {
                                            if (!result[item].length) return null;
                                            return (
                                                <div key={item} class="popover-infectious-wrapper">
                                                    <div class="popover-title-color">{gradeFilter(item)}：</div>
                                                    <div class="infectious-diseases-wrapper">
                                                        {
                                                            result[item].map((ele, index) => {
                                                                return (
                                                                    <div key={index} class="disease-text-wrapper">
                                                                        <span>{ele}</span>
                                                                    </div>
                                                                );
                                                            })
                                                        }
                                                    </div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            </abc-popover>
                            <div style={{
 flex: 1, 'margin-left': '8px',
}} { ...{ directives } }></div>
                        </div>
                    );
                }
                return (
                    <div
                        style={{
                            padding: '0px 10px',
                        }}
                        { ...{ directives } }
                    ></div>
                );
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .table-container {
        .vip::after {
            display: inline-block;
            width: 12px;
            height: 12px;
            vertical-align: middle;
            content: '';
            background: url('~assets/images/vip.png') no-repeat;
            background-size: 12px 12px;
        }
    }

    .common-padding-container {
        .cell.oral-examination {
            > span {
                display: inline-block;
                padding: 4px;
                font-family: Roboto;
                font-size: 12px;
                line-height: 12px;
            }

            span.top-left {
                border-right: 1px solid #878c92;
                border-bottom: 1px solid #878c92;
            }

            span.top-right {
                border-bottom: 1px solid #878c92;
                border-left: 1px solid #878c92;
            }

            span.bottom-left {
                border-top: 1px solid #878c92;
                border-right: 1px solid #878c92;
            }

            span.bottom-right {
                border-top: 1px solid #878c92;
                border-left: 1px solid #878c92;
            }

            .bottom-left + .bottom-right,
            .top-left + .top-right {
                border-left: 0;
            }
        }
    }

    .op-list-table {
        td {
            max-width: 200px;
        }
    }

    .outpatient-stat__prescription-summary {
        display: inline-block;
        max-width: 180px;
        padding: 0 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ms-list-popper {
        max-height: 640px;
        overflow-y: auto;
        overflow-y: overlay;
        font-size: 12px;
        color: $T2;

        .prescription {
            padding-bottom: 8px;
            margin-bottom: 12px;
            border-bottom: 1px solid $P6;

            &:last-child {
                padding-bottom: 0;
                margin-bottom: 0;
                border-bottom: none;
            }

            .title {
                font-size: 13px;
                font-weight: bold;
                color: #000000;
            }

            .content {
                padding: 4px 0;

                li {
                    display: flex;
                    justify-content: space-between;
                }
            }
        }

        .glasses-form-wrapper {
            max-width: 532px;
            margin-top: 4px;
            border: 1px solid $P6;

            &.is-contact {
                max-width: 100%;
            }

            &-content {
                display: flex;
                width: 100%;

                .glasses-form-item {
                    display: flex;
                    flex-direction: column;
                    border-right: 1px solid $P6;

                    &:last-child {
                        border-right: none;
                    }

                    span {
                        display: inline-block;
                        min-width: 40px;
                        max-width: 70px;
                        height: 24px;
                        padding-right: 8px;
                        padding-left: 8px;
                        overflow: hidden;
                        line-height: 24px;
                        text-align: center;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        border-bottom: 1px solid $P6;

                        &.is-frame {
                            width: 100px;
                        }

                        &.is-base {
                            max-width: 130px;
                        }

                        &:last-child {
                            border-bottom: none;
                        }
                    }
                }
            }

            &-footer {
                display: flex;
                justify-content: space-between;
                width: 100%;
                height: 24px;
                padding: 0 10px;
                line-height: 24px;
                border-top: 1px solid $P6;
            }
        }
    }

    .infectious-disease-popover-wrapper {
        display: flex;
        flex-direction: column;
        min-width: 250px;
        max-width: 300px;
        padding: 5px;

        .popover-infectious-wrapper {
            display: flex;

            &:not(:first-of-type) {
                margin-top: 10px;
            }

            .infectious-diseases-wrapper {
                display: flex;
                flex: 1;
                flex-wrap: wrap;
            }

            .disease-text-wrapper {
                &:not(:last-of-type)::after {
                    content: '、';
                }
            }
        }

        .popover-title-color {
            color: $T2;
        }
    }
</style>
