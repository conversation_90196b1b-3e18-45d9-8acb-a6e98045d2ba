import Qs from 'qs';
import Logger from 'utils/logger';
import {
    ABCYUN_TOKEN_STORAGE,
} from 'utils/local-storage-key.js';
import { getApp } from '@/core/index.js';
import Clone from '@/utils/clone';
// eslint-disable-next-line camelcase
import { aes_decrypt } from 'abc-safe-pkg';

function isNumber(a) {
    return parseFloat(a).toString() !== 'NaN';
}

export const HEADER_ABC_TS = '__abc-ts__';
export const REQUEST_PATH = '__abc-path__';
export const REQUEST_TOKEN = '__abc-secret__';

export default class SignService {
    static async generateSignature({
        protocol, config, timestamp, abcSecret,
    }) {
        const {
            url, params, paramsSerializer,
        } = config;
        const abcSecretFinal = abcSecret || window.localStorage.getItem(ABCYUN_TOKEN_STORAGE);

        let reqUrl = '';
        if (url.includes(`//${location.host}`)) {
            reqUrl = `${protocol}${url}`;
        } else {
            reqUrl = `${protocol}//${location.host}${url}`;
        }
        if (params) {
            if (paramsSerializer) {
                reqUrl += `&${paramsSerializer(params)}`;
            } else {
                reqUrl += `&${Qs.stringify(params)}`;
            }
        }
        //解析url
        const uri = new URL(reqUrl);
        const {
            pathname, searchParams,
        } = uri;
        //构建签名参数map
        const signParams = new Map();
        signParams.set(HEADER_ABC_TS, timestamp);
        signParams.set(REQUEST_TOKEN, abcSecretFinal);
        signParams.set(REQUEST_PATH, pathname);
        // 遍历查询参数
        if (searchParams && searchParams.toString()) {
            for (const [key, value] of searchParams.entries()) {
                if (isNumber(value) || value) {
                    signParams.set(key, value);
                }
            }
        }
        return SignService.signUtils(signParams, {
            timestamp,
            pathname,
            config,
            abcSecret,
        });
    }

    static async signUtils(paramMap, options) {
        const {
            timestamp, pathname, config, abcSecret,
        } = options;
        const { paramsSerializer } = config;
        let signStr = '';
        try {
            const AbcSafePkg = await import('abc-safe-pkg');
            signStr = AbcSafePkg.sign(paramMap, paramsSerializer);
        } catch (e) {
            console.error(e);
            Logger.report({
                scene: 'api_sign',
                data: {
                    error: e.message,
                    paramMap: Object.fromEntries(paramMap),
                },
            });
        }
        if (
            pathname.includes('/api/v2/outpatients/history') ||
            pathname.includes('/api/v3/goods/goods-list/non-stock-goods') ||
            pathname.includes('/api/v2/sc/stat') ||
            pathname.includes('/api/v3/goods/prices/orders') ||
            pathname.includes('/api/v3/goods/goods-list/stock-goods') ||
            pathname.includes('/api/v3/goods/goods-list/stock-goods/export') ||
            pathname.includes('/api/v2/sc/cdss') ||
            pathname.includes('/api/v2/cdss') ||
            pathname.includes('/api/v2/shebao-stat/national') ||
            pathname.includes('/api/v2/crm/patients')
        ) {
            Logger.report({
                scene: 'api_sign',
                data: {
                    timestamp,
                    pathname,
                    signStr,
                    source: {
                        url: config.url,
                        params: config.params,
                    },
                    store: window.localStorage.getItem(ABCYUN_TOKEN_STORAGE),
                    cookie: abcSecret,
                },
            });
        }
        return signStr;
    }

    /**
     * desc 解密数据
     * @param dataStr
     * @param key
     * @returns {Promise<string>}
     */
    static aecSignForDec(dataStr, key) {
        let signStr = '';
        try {
            signStr = aes_decrypt(dataStr, key);
        } catch (e) {
            signStr = dataStr;
            console.error(e);
            Logger.report({
                scene: 'api_sign_aes_decrypt',
                data: {
                    error: e.message,
                    dataStr,
                },
            });
        }
        return signStr;
    }

    /**
     * @desc 为原始对象中的指定key解码
     * @date 2025/09/18 09:44:35
     * @param {Object} originObj 原始对象
     * @param {Object} decodeKeyObj 原始对象中需要解密的key
     * @return {Object}
     */
    static decodeAESObjectForKey(originObj, decodeKeyObj) {
        function process(obj, spec, currentPath = []) {
            for (const key in spec) {
                if (Object.prototype.hasOwnProperty.call(spec, key)) {
                    const specValue = spec[key];
                    if (obj[key] === undefined || obj[key] === null) {
                        continue;
                    }
                    if (typeof specValue === 'object' && !Array.isArray(specValue)) {
                        if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                            process(obj[key], specValue, [...currentPath, key]);
                        }
                    } else if (specValue === true && typeof obj[key] === 'string') {
                        obj[key] = SignService.aecSignForDec(obj[key], getApp().store.getters['theme/curTypeId']);
                    }
                }
            }
        }
        try {
            if (!originObj || !decodeKeyObj) return originObj;
            const result = Clone(originObj);
            process(result, decodeKeyObj);
            return result;
        } catch (e) {
            Logger.error({
                scene: 'decodeAESObjectForKey err',
                err: e,
            });
            return originObj;
        }
    }
}
