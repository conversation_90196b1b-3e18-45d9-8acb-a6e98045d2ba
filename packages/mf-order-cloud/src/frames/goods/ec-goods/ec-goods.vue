<template>
    <abc-layout preset="page-table" style="height: calc(100% - 48px);">
        <abc-layout-header>
            <abc-flex :gap="8">
                <abc-space is-compact>
                    <abc-select
                        v-model="params.ecType"
                        :width="92"
                        trigger-icon="s-triangle-select-color"
                        :input-style="{ fontWeight: 600 }"
                    >
                        <abc-image
                            slot="prepend"
                            :src="WpCodeLogo.PDD"
                            :width="16"
                        ></abc-image>
                        <abc-option
                            v-for="(op) in ecTypeOptions"
                            :key="op.value"
                            :value="op.value"
                            :label="op.label"
                        ></abc-option>
                    </abc-select>
                    <biz-select-tabs
                        v-model="params.mallId"
                        :options="mallList"
                        :width="240"
                        :max-width="240"
                        @change="handleMallChange"
                    >
                    </biz-select-tabs>
                </abc-space>

                <abc-tabs-v2
                    v-model="params.goodsStatus"
                    :option="goodsStatusOptions"
                    size="middle"
                    type="outline"
                    @change="getGoodsList"
                ></abc-tabs-v2>

                <abc-input
                    v-model="params.keyword"
                    placeholder="商品名称/商品ID"
                    :icon="params.keyword ? 'cis-icon-cross_small' : ''"
                    @icon-click="clearInput"
                    @input="handleSearchGoods"
                ></abc-input>

                <abc-cascader
                    ref="catTypeRef"
                    v-model="catIdList"
                    :props="{
                        children: 'children',
                        label: 'catName',
                        value: 'catId',
                    }"
                    placeholder="商品分类"
                    multiple
                    :width="200"
                    mutually-exclusive
                    :options="goodsTypesList"
                    @change="handleGoodsTypeChange"
                >
                </abc-cascader>

                <abc-space is-compact :border-style="false" split>
                    <div class="price-container">
                        价格
                    </div>
                    <abc-input
                        v-model="params.goodsSkuPriceStart"
                        :width="90"
                        clearable
                        type="number"
                        :config="{
                            formatLength: 2, supportZero: true, max: 99999
                        }"
                        :input-custom-style="{
                            textAlign: 'center'
                        }"
                        placeholder="最低"
                        @input="inputHandler"
                    ></abc-input>
                    <div class="separator-container">
                        ~
                    </div>
                    <abc-input
                        v-model="params.goodsSkuPriceEnd"
                        style="margin-right: 6px;"
                        :width="90"
                        type="number"
                        clearable
                        :config="{
                            formatLength: 2, supportZero: true, max: 99999
                        }"
                        :input-custom-style="{
                            textAlign: 'center'
                        }"
                        placeholder="最高"
                        @input="inputHandler"
                    ></abc-input>
                </abc-space>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                :loading="contentLoading"
                cell-size="xxxlarge"
                cell-padding-size="large"
                header-size="large"
                :show-content-empty="true"
                :empty-content="mallList && mallList.length ? '暂无数据' : '未绑定网店，无法同步商品'"
                :render-config="tableConfig"
                :data-list="tableData"
                :tr-click-trigger-checked="false"
                :show-checked="false"
                :need-selected="false"
            >
                <abc-flex
                    v-if="!isSearchingGoods"
                    slot="topHeader"
                    justify="space-between"
                    align="center"
                >
                    <biz-mixed-selection-filter
                        v-model="paramsConditionSelection"
                        type="radio"
                        :options="warningList"
                        @change="handleChangeWarningType"
                    ></biz-mixed-selection-filter>
                </abc-flex>
                <template
                    #name="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-popover
                        placement="bottom-start"
                        trigger="hover"
                        :visible-arrow="false"
                        theme="yellow"
                        width="365px"
                        size="large"
                        :popper-style="{
                            marginTop: item.spuSpan > 1 ? `-${(item.spuSpan - 1) * 72}px` : ''
                        }"
                        padding-size="large"
                        class="ec-goods-detail__popover"
                    >
                        <abc-table-cell
                            slot="reference"
                            :cell-row-span="cellRowSpan"
                        >
                            <div class="goods-item" style="cursor: pointer;">
                                <div class="goods-item-left">
                                    <img
                                        :src="item.originItem?.imageUrl"
                                        alt=""
                                        width="48"
                                        height="48"
                                    />
                                </div>
                                <div class="goods-item-right">
                                    <div class="goods-name">
                                        <abc-tag-v2
                                            variant="outline"
                                            min-width="54"
                                            size="mini"
                                            :theme="getGoodsStatusTheme(item.originItem.status)"
                                        >
                                            {{ getGoodsStatusText(item.originItem.status) }}
                                        </abc-tag-v2>
                                        <span>{{ item.originItem.name }}</span>
                                    </div>
                                </div>
                            </div>
                        </abc-table-cell>
                        <div class="ec-goods-detail__popover—header">
                            <div class="title">
                                商品信息
                            </div>
                            <div class="goods-item">
                                <div class="goods-item-left">
                                    <img
                                        :src="item.originItem.imageUrl"
                                        alt=""
                                        width="48"
                                        height="48"
                                    />
                                </div>
                                <div class="goods-item-right">
                                    <div class="goods-name">
                                        <abc-tag-v2
                                            :variant="'outline'"
                                            :min-width="54"
                                            :size="'mini'"
                                            :theme="getGoodsStatusTheme(item.originItem.status)"
                                        >
                                            {{ getGoodsStatusText(item.originItem.status) }}
                                        </abc-tag-v2>
                                        <span>{{ item.originItem.name }}</span>
                                    </div>
                                </div>
                            </div>

                            <abc-divider variant="dashed" margin="mini"></abc-divider>
                            <ul class="ec-goods-detail__popover-content">
                                <li v-for="goods in getGoodsInfo(item.originItem)" :key="goods.label">
                                    <span class="label">{{ goods.label }}</span>
                                    <span class="value ellipsis" :title="goods.value">{{ goods.value }}</span>
                                </li>
                            </ul>

                            <div class="ec-goods-detail__popover-footer">
                                <abc-button variant="ghost" size="small" @click="copyGoodsId($event, item.originItem)">
                                    复制商品ID
                                </abc-button>
                                <abc-button variant="ghost" size="small" @click="editGoods(item.originItem)">
                                    编辑商品
                                </abc-button>
                            </div>
                        </div>
                    </abc-popover>
                </template>

                <template
                    #sku="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        :cell-row-span="cellRowSpan"
                        style="padding: 0;"
                    >
                        <div
                            class="ec-goods-table-cell__row"
                        >
                            {{ item.skuItem.name }}
                        </div>
                    </abc-table-cell>
                </template>
                <template
                    #price="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div
                            class="ec-goods-table-cell__row price"
                        >
                            <abc-text>拼单价：{{ item.skuItem.multiPrice | formatMoney }}</abc-text>
                            <abc-text>单买价：{{ item.skuItem.price | formatMoney }}</abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template
                    #quantity="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div
                            class="ec-goods-table-cell__row quantity"
                        >
                            <abc-text v-if="item.skuItem.bindStatus" :theme="item.skuItem.stockWarnFlag ? 'warning' : 'black'">
                                {{ item.skuItem.quantity }}
                            </abc-text>
                            <div v-else>
                                -
                            </div>
                        </div>
                    </abc-table-cell>
                </template>
                <template
                    #lastMonthSellCount="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div
                            class="ec-goods-table-cell__row quantity"
                        >
                            <abc-text>
                                {{ item.skuItem.lastMonthSellCount }}
                            </abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template
                    #turnoverDays="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div
                            class="ec-goods-table-cell__row quantity"
                        >
                            <abc-text :theme="item.skuItem.stockWarnFlag ? 'warning' : 'black'">
                                {{ item.skuItem.turnoverDays ? `${item.skuItem.turnoverDays}天` : '-' }}
                            </abc-text>
                        </div>
                    </abc-table-cell>
                </template>
                <template #bindGoodsName="{ trData: item }">
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                    >
                        <div
                            class="ec-goods-table-cell__row bind-goods"
                        >
                            <div v-if="item.skuItem.relHisGoodsList.length === 0">
                                <abc-text theme="warning" class="unbind-text">
                                    未绑定
                                </abc-text>
                            </div>
                            <template v-else>
                                <abc-text v-abc-title.ellipsis="item.hisGoodsInfo?.displayName || ''" class="goods-name">
                                </abc-text>
                                <abc-text
                                    v-abc-title.ellipsis="`${item.hisGoodsInfo?.displaySpec || ''}  ${ item.hisGoodsInfo?.manufacturer || ''}`"
                                    size="mini"
                                    theme="gray"
                                    class="goods-spec"
                                >
                                </abc-text>
                            </template>
                        </div>
                    </abc-table-cell>
                </template>
                <template #bindGoodsCount="{ trData: item }">
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                    >
                        <div
                            class="ec-goods-table-cell__row bind-goods-count"
                        >
                            <div v-if="item.skuItem.relHisGoodsList.length === 0">
                                -
                            </div>
                            <template v-else>
                                <div>
                                    <abc-text class="goods-info-item">
                                        {{ item?.useDismounting ? item?.bindPieceCount : item?.bindPackageCount }}
                                    </abc-text>
                                </div>
                            </template>
                        </div>
                    </abc-table-cell>
                </template>
                <template #bindGoodsUnit="{ trData: item }">
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                    >
                        <div
                            class="ec-goods-table-cell__row bind-goods-unit"
                        >
                            <div v-if="item.skuItem.relHisGoodsList.length === 0" class="ec-goods-table-cell__row-goods-info">
                                -
                            </div>
                            <template v-else>
                                <div class="ec-goods-table-cell__row-goods-info">
                                    <abc-text>
                                        {{ item?.unit }}
                                    </abc-text>
                                </div>
                            </template>
                        </div>
                    </abc-table-cell>
                </template>
                <template #abcQuantity="{ trData: item }">
                    <abc-table-cell
                        class="ec-goods-table-cell"
                        vertical
                        justify="center"
                        style="padding: 0;"
                    >
                        <div
                            class="ec-goods-table-cell__row bind-goods-count"
                        >
                            <div v-if="item.skuItem.relHisGoodsList.length === 0" class="ec-goods-table-cell__row-goods-info">
                                -
                            </div>
                            <template v-else>
                                <div class="ec-goods-table-cell__row-goods-info">
                                    <abc-text class="goods-info-item">
                                        {{ item?.hisGoodsInfo.dispStockGoodsCount }}
                                    </abc-text>
                                </div>
                            </template>
                        </div>
                    </abc-table-cell>
                </template>
                <template #allocationStock="{ trData: item }">
                    <abc-table-cell v-if="item.skuItem.relHisGoodsList.length === 0">
                        -
                    </abc-table-cell>
                    <table-cell-allocation-stock
                        v-else
                        :ec-type="ECTypeEnum.PDD"
                        :item="item"
                        :clinic-id="bindClinicId"
                        :disabled="!isAdmin"
                        @change="changeStockHandler"
                    ></table-cell-allocation-stock>
                </template>
                <template
                    #operation="{
                        trData: item, cellRowSpan
                    }"
                >
                    <abc-table-cell
                        vertical
                        justify="center"
                        style="padding: 0;"
                        :cell-row-span="cellRowSpan"
                    >
                        <div style="width: 100%;">
                            <abc-button
                                v-if="isAdmin"
                                slot="reference"
                                variant="text"
                                @click="handleBindGoods(item)"
                            >
                                {{ item.skuItem.relHisGoodsList?.length ? '换绑' : '绑定' }}
                            </abc-button>
                            <abc-tooltip v-else placement="top-start" content="请联系总部操作">
                                <abc-button type="ghost" disabled variant="text">
                                    {{ item.skuItem.relHisGoodsList?.length ? '换绑' : '绑定' }}
                                </abc-button>
                            </abc-tooltip>
                        </div>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="paginationParams"
                :count="totalCount"
                :show-total-page="true"
                :page-sizes="[20, 50, 100]"
                show-size
                @current-change="pageChange"
                @size-change="sizeChange"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ECGoodsAPI from '@/api/goods';
    import ECAuthAPI from '@/api/auth';
    import { mapGetters } from 'vuex';
    import { debounce } from 'MfBase/lodash';
    import { copy } from '@abc/utils-dom';
    import DialogBindGoods from '../../../components/dialog-bind-goods';
    import { formatMoney } from '@abc/utils';
    import {
        EcShopTypeEnum, ECTypeEnum,
        WpCodeLogo,
    } from '@/utils/constants';
    import TableCellAllocationStock from '@/components/table-cell-allocation-stock.vue';
    import BizSelectTabs from 'MfBase/biz-select-tabs';
    import BizMixedSelectionFilter from 'MfBase/biz-mixed-selection-filter';
    export default {
        name: 'EcGoodsTable',
        components: {
            TableCellAllocationStock,
            BizSelectTabs,
            BizMixedSelectionFilter,
        },
        data() {
            return {
                WpCodeLogo,
                paramsConditionSelection: '',
                catIdList: [],
                goodsSkuStockWarnFlag: 0,
                goodsSkuBindStatus: 0,
                params: {
                    limit: 100,
                    offset: 0,
                    mallId: '',
                    catId: '',
                    goodsStatus: 20,
                    keyword: '',
                    goodsSkuPriceStart: '',
                    goodsSkuPriceEnd: '',
                    goodsSkuStockWarnFlag: '',
                    goodsSkuBindStatus: '',
                    ecType: ECTypeEnum.PDD,
                },
                totalCount: 0,
                tableData: [],
                tableTrHeight: 72,
                contentLoading: false,
                clinicList: [],
                mallList: [],
                goodsTypesList: [],
                summaryData: {},
                bindClinicId: '',
                tableMaxHeight: '400px',
                isSearchingGoods: false,
                ECTypeEnum,
                ecTypeOptions: [
                    {
                        label: '拼多多',
                        value: ECTypeEnum.PDD,
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isAdmin',
            ]),
            warningList() {
                return [
                    {
                        label: '未绑定',
                        value: 'goodsSkuBindStatus',
                        statisticsNumber: this.summaryData.unbindGoodsSkuCount || 0,
                    },
                    {
                        label: '库存预警',
                        value: 'goodsSkuStockWarnFlag',
                        statisticsNumber: this.summaryData.goodsSkuStockWarnCount || 0,
                    },
                    {
                        label: '未分配库存',
                        value: 'assignStockWarnFlag',
                        statisticsNumber: this.summaryData.assignStockWarnCount || 0,
                    },
                ];
            },
            tableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'name',
                            label: '网店商品名称',
                            style: {
                                flex: 3,
                                minWidth: '200px',
                            },
                            rowSpan: (trData) => {
                                return trData.spuSpan;
                            },
                        },
                        {
                            key: 'sku',
                            label: 'SKU',
                            style: {
                                flex: 2,
                                minWidth: '200px',
                            },
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                        {
                            key: 'price',
                            label: '价格',
                            style: {
                                flex: 'none',
                                width: '152px',
                            },
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                        {
                            key: 'lastMonthSellCount',
                            label: '30日销量',
                            style: {
                                flex: 'none',
                                width: '90px',
                                textAlign: 'right',
                            },
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                        {
                            key: 'turnoverDays',
                            label: '周转',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'right',
                            },
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                        {
                            key: 'quantity',
                            label: '库存',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'right',
                            },
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                        {
                            key: 'bindGoodsName',
                            label: 'ABC绑定商品',
                            style: {
                                flex: 2,
                                minWidth: '200px',
                            },
                        },
                        {
                            key: 'bindGoodsCount',
                            label: '绑定数量',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'bindGoodsUnit',
                            label: '绑定单位',
                            style: {
                                flex: 'none',
                                width: '80px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'abcQuantity',
                            label: 'ABC 库存',
                            style: {
                                flex: 'none',
                                width: '108px',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'allocationStock',
                            label: '分配的ABC库存',
                            style: {
                                flex: 'none',
                                width: '130px',
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            style: {
                                flex: 'none',
                                width: '64px',
                                textAlign: 'center',
                            },
                            pinned: 'right',
                            rowSpan: (trData) => {
                                return trData.skuSpan;
                            },
                        },
                    ],
                };
            },

            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.params;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                    count: this.totalCount,
                };
            },
            goodsStatusOptions() {
                const {
                    offShelfCount,
                    onShelfCount,
                    sellOutCount,
                } = this.summaryData || {};
                return [
                    {
                        label: '全部',
                        value: 0,
                        statisticsNumber: 0,
                    },
                    {
                        label: '在售中',
                        value: 20,
                        statisticsNumber: onShelfCount || 0,
                    },
                    {
                        label: '已下架',
                        value: 10,
                        statisticsNumber: offShelfCount || 0,
                    },
                    {
                        label: '已售罄',
                        value: 30,
                        statisticsNumber: sellOutCount || 0,
                    },
                ];
            },
        },
        async created() {
            await this.getMallList();
            this.params.mallId = this.mallList?.[0]?.ecMallId || '';
            this.bindClinicId = this.mallList.find((item) => item.ecMallId === this.params.mallId)?.bindClinicId || '';
            this.fetchGoodsTypesList();
            this.getGoodsList();
            this._debounceSearch = debounce(
                () => {
                    this.getGoodsList();
                },
                250,
                true,
            );
            this._handleClickTag = debounce(
                (val, key) => {
                    this.handleClickTag(val, key);
                },
                500,
                true,
            );
            this.$abcEventBus.$on('refresh-ec-goods', () => this.getGoodsList(), this);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            formatMoney,
            handleChangeWarningType(val) {
                this.params.goodsSkuBindStatus = '';
                this.params.goodsSkuStockWarnFlag = '';
                this.params.assignStockWarnFlag = '';
                let checked = 0;
                if (val) {
                    checked = 1;
                }
                this._handleClickTag(checked, val);
            },
            changeStockHandler() {
                this.getGoodsList();
            },
            pageChange(pageIndex) {
                this.params.offset = (pageIndex - 1) * this.params.limit;
                this.getGoodsList();
            },
            copyGoodsId(event, item) {
                copy(`${item.externalGoodsId}`);
                this.$Toast({
                    message: '复制成功',
                    type: 'info',
                    referenceEl: event.target,
                });
            },
            editGoods(item) {
                const currentWidth = window.innerWidth;
                const currentHeight = window.innerHeight;

                const newWidth = Math.round(currentWidth * 0.8);
                const newHeight = Math.round(currentHeight * 0.8);
                window.open(`https://mms.pinduoduo.com/goods/goods_detail?goods_id=${item.externalGoodsId}`,'',`width=${newWidth},height=${newHeight}`);
            },
            getGoodsInfo(item) {
                const {
                    name,
                    brand,
                    manufacturer,
                    spec,
                    medicineNmpn,
                    medicineClassify,
                    externalGoodsId,
                    dosage,
                } = item;
                return [
                    {
                        label: '商品ID',
                        value: externalGoodsId,
                    },
                    {
                        label: '通用名',
                        value: name,
                    },
                    {
                        label: '品牌',
                        value: brand || '-',
                    },
                    {
                        label: '生产企业',
                        value: manufacturer || '-',
                    },
                    {
                        label: '药品规格',
                        value: spec || '-',
                    },
                    {
                        label: '批准文号',
                        value: medicineNmpn || '-',
                    },
                    {
                        label: '药品分类',
                        value: medicineClassify || '-',
                    },
                    {
                        label: '剂型',
                        value: dosage || '-',
                    },
                ];

            },
            sizeChange(pageSize) {
                this.params.limit = pageSize;
                this.getGoodsList();
            },
            handleMounted(data) {
                this.tableMaxHeight = `${data.height}px`;
            },
            onChangeTab() {},
            handleClickTag(val, key) {
                if (key) this.params[key] = val;
                this.getGoodsList();
            },
            getGoodsStatusText(status) {
                if (status === 0) return '在售中';
                if (status === 10) return '已下架';
                if (status === 20) return '在售中';
                if (status === 30) return '已售罄';
                if (status === 99) return '已删除';
            },
            getGoodsStatusTheme(status) {
                if (status === 0) return 'success';
                if (status === 20) return 'success';
                if (status === 10) return 'danger';
                if (status === 30) return 'warning';
                if (status === 99) return 'warning';
            },
            clearInput() {
                this.params.keyword = '';
                this.inputHandler();
                this.isSearchingGoods = false;
            },

            handleBindGoods(item) {
                if (!this.isAdmin) return;
                new DialogBindGoods({
                    clinicId: this.bindClinicId,
                    ecMallId: this.params.mallId,
                    mallName: this.mallList?.find((it) => it.ecMallId === this.params.mallId)?.mallName,
                    goodsSkuId: item.skuItem.id,
                    goodsId: item.skuItem.goodsId,
                    goodsName: item.originItem.name || '-',
                    desc: item.skuItem.name || '-',
                    hisGoodsList: item.skuItem.relHisGoodsList || [],
                }).generateDialogAsync({ parent: this });
            },
            handleSearchGoods() {
                this.isSearchingGoods = true;
                this.params.goodsSkuPriceStart = '';
                this.params.goodsSkuPriceEnd = '';
                this.params.goodsSkuStockWarnFlag = '';
                this.params.goodsSkuBindStatus = '';
                this.params.catId = '';
                this._debounceSearch();
            },
            inputHandler() {
                this._debounceSearch();
            },
            handleMallChange() {
                this.bindClinicId = this.mallList.find((item) => item.ecMallId === this.params.mallId)?.bindClinicId || '';
                this.getGoodsList();
                this.fetchGoodsTypesList();
            },

            async getMallList() {
                try {
                    const res = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                        onlyUnexpired: 1, // 在授权有效期内
                        includeHistory: 1, // 是否包含历史绑定的电商店铺
                        includeDeleted: 1, // 是否包含已删除的绑定数据
                    });
                    this.mallList = (res?.rows || [])
                        .filter((item) => item.shopType === EcShopTypeEnum.B2C)
                        .sort((a, b) => {
                            const getOrder = (order) => (order === 1 ? 1 : 0);
                            const orderA = getOrder(a.status);
                            const orderB = getOrder(b.status);
                            return orderB - orderA;
                        })
                        .reduce((pre, cur) => {
                            if (pre.find((item) => item.ecMallId === cur.ecMallId)) return pre;
                            pre.push(cur);
                            return pre;
                        }, [])
                        .map((item) => {
                            return {
                                ...item,
                                key: item.ecMallId,
                                label: `${item.mallName}（${item.bindClinicName}）`,
                                value: item.ecMallId,
                                icon: 's-s-drugstore-color',
                            };
                        });

                } catch (err) {
                    console.error(err);
                }
            },
            handleGoodsTypeChange(list) {
                const newList = list.filter((item) => item !== null);
                this.params.catId = newList.flat().map((item) => item.value).join(',');
                this.getGoodsList();
            },
            async fetchGoodsTypesList() {
                try {
                    const res = await ECGoodsAPI.getGoodsTypes({
                        mallId: this.params.mallId,
                        ecType: 1, // 现在只有拼多多
                    });
                    if (res) {
                        this.goodsTypesList = res;
                    }
                } catch (err) {
                    console.error(err);
                }
            },

            calcCount(item) {
                return item.skuList.reduce((acc, sku) => {
                    return acc + (sku.relHisGoodsList?.length || 1);
                }, 0);
            },

            transRowsToRenderRows(list = []) {
                const arr = [];
                list.forEach((item) => {

                    // spuSpan 需要一直往下找到 relHisGoodsList 的长度
                    const goodsCount = this.calcCount(item);

                    item.skuList = item.skuList || [];
                    item.skuList.forEach((sku, skuIndex) => {

                        // skuSpan 只用找 relHisGoodsList 的长度
                        const skuGoodsCount = sku.relHisGoodsList?.length;

                        // 有绑定的his goods需要展开，否则就展示一条sku
                        if (skuGoodsCount) {
                            sku.relHisGoodsList.forEach((goods, goodsIndex) => {
                                arr.push({
                                    ...goods,
                                    skuItem: sku,
                                    originItem: item,
                                    groupId: item.id,
                                    spuSpan: skuIndex === 0 && goodsIndex === 0 ? goodsCount : 1,
                                    skuSpan: goodsIndex === 0 ? skuGoodsCount : 1,
                                });
                            });
                        } else {
                            arr.push({
                                ...sku,
                                skuItem: sku,
                                originItem: item,
                                groupId: item.id,
                                spuSpan: skuIndex === 0 ? goodsCount : 1,
                                skuSpan: skuIndex === 0 ? skuGoodsCount : 1,
                            });
                        }
                    });
                });
                console.log(arr);
                return arr;
            },

            async getGoodsList() {
                try {
                    if (!this.params.mallId) return;
                    this.contentLoading = true;
                    const { params } = this;
                    const res = await ECGoodsAPI.getEcGoods({
                        ...params,
                        goodsSkuStockWarnFlag: params.goodsSkuStockWarnFlag === 1 ? 1 : '',
                        goodsSkuBindStatus: params.goodsSkuBindStatus === 1 ? 0 : '',
                        assignStockWarnFlag: params.assignStockWarnFlag === 1 ? 1 : undefined,
                    });
                    this.tableData = this.transRowsToRenderRows(res.rows);
                    console.log('this.tableData', this.tableData);
                    this.totalCount = res.total || 0;
                    this.summaryData = res?.summary || {
                        sellOutCount: 0,
                        offShelfCount: 0,
                        onShelfCount: 0,
                        unbindGoodsSkuCount: 0,
                        goodsSkuStockWarnCount: 0,
                        assignStockWarnCount: 0,
                    };
                } catch (err) {
                    console.error(err);
                } finally {
                    this.contentLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
.pharmacy__ec-goods-table-section {
    .ec-goods-table-cell {
        &__row {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 72px;
            padding: 10px;

            &.price {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
            }

            &.quantity {
                justify-content: flex-end;
            }

            &.bind-goods {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: center;
                padding: 0 10px;
            }

            &.bind-goods-count {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                justify-content: center;
                padding: 0 10px;
            }

            &.bind-goods-unit {
                text-align: center;
            }

            &-goods-info {
                width: 100%;
                padding: 10px;

                .unbind-text {
                    display: flex;
                    align-items: center;
                    height: 100%;
                }

                .goods-info-item {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    height: 100%;
                }

                .goods-info-item-unit {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                }

                .goods-name {
                    display: block;
                }
            }
        }
    }

    .price-container {
        box-sizing: border-box;
        width: 50px;
        height: 32px;
        padding-left: 6px;
        overflow: hidden;
        line-height: 31px;
        text-align: center;
        background: #ffffff;
        border: 1px solid #e6eaee;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
    }

    .separator-container {
        box-sizing: border-box;
        width: 30px;
        height: 32px;
        overflow: hidden;
        line-height: 31px;
        color: var(--abc-color-T3);
        text-align: center;
        background: #ffffff;
        border: 1px solid #e6eaee;
        border-left: none;
    }
}

.ec-goods-detail__popover—header {
    .title {
        font-weight: bold;
    }

    .goods-item {
        display: flex;
        padding-top: 12px;

        .goods-item-left {
            display: inline-block;
        }

        .goods-item-right {
            margin-left: 10px;

            .goods-name {
                span {
                    margin-left: 4px;
                }
            }
        }
    }
}

.ec-goods-detail__popover {
    .goods-item {
        display: flex;
        align-items: center;
        padding: 9px 0;

        .goods-item-right {
            margin-left: 10px;

            .goods-name {
                span {
                    margin-left: 4px;
                }
            }
        }
    }

    &-content {
        margin-top: 10px;

        li {
            display: flex;
            align-items: center;

            .label {
                display: inline-block;
                width: 66px;
                color: #7a8794;
            }

            .value {
                display: inline-block;
                max-width: 260px;
            }
        }
    }

    &-footer {
        text-align: end;
    }
}
</style>
