<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        preset="step"
        size="xlarge"
        title="接入美团外卖网店"
        class="ec-bind-modal"
        :show-footer="false"
    >
        <template #step>
            <abc-steps :active="currentActive">
                <abc-step :index="0">
                    {{ currentActive === 0 ? '获取' : '接入' }}网店授权
                </abc-step>
                <abc-step :index="1">
                    同步网店商品
                </abc-step>
            </abc-steps>
        </template>
        <abc-flex justify="center" style="width: 100%; height: 345px; padding: 0 16px;">
            <template v-if="readyLoading">
                <abc-flex
                    style="width: 100%; height: 100%;"
                    justify="center"
                    align="center"
                >
                    <abc-loading-spinner middle></abc-loading-spinner>
                </abc-flex>
            </template>
            <template v-else>
                <abc-flex
                    v-if="currentActive === 0"
                    vertical
                    justify="center"
                    align="center"
                    :gap="59"
                >
                    <template v-if="bindClinicInfo">
                        <abc-space size="large">
                            <abc-flex
                                vertical
                                align="center"
                                justify="center"
                                gap="large"
                                style="width: 338px;"
                            >
                                <div v-if="bindClinicInfo.accountInfo.headImgUrl" style="position: relative;">
                                    <abc-image
                                        :src="logoIconMeiTuan"
                                        :width="32"
                                        :height="21"
                                        style="position: absolute; top: -4px; left: -12px; z-index: 1;"
                                    ></abc-image>
                                    <abc-image
                                        :src="bindClinicInfo.accountInfo.headImgUrl"
                                        :width="48"
                                        :height="48"
                                        style="border: 1px solid var(--abc-color-P6); border-radius: 8px; object-fit: cover;"
                                    ></abc-image>
                                </div>
                                <abc-image
                                    v-else
                                    :src="WpCodeLogo.MT"
                                    :width="48"
                                    :height="48"
                                    alt="美团"
                                ></abc-image>
                                <abc-text size="largex" bold>
                                    {{ bindClinicInfo.accountInfo.wmPoiName }}
                                </abc-text>
                            </abc-flex>
                            <abc-icon icon="s-switch-line" size="24" color="var(--abc-color-theme1)"></abc-icon>
                            <abc-flex
                                vertical
                                align="center"
                                justify="center"
                                gap="large"
                                style="width: 338px;"
                            >
                                <div style="padding: 8px;">
                                    <abc-icon icon="s-organization-color" size="32"></abc-icon>
                                </div>
                                <abc-text size="largex" bold>
                                    {{ bindClinic.clinicName }}
                                </abc-text>
                            </abc-flex>
                        </abc-space>


                        <abc-button
                            size="large"
                            :loading="btnLoading"
                            @click="handleBind"
                        >
                            确认授权，下一步
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-flex
                            vertical
                            align="center"
                            gap="large"
                        >
                            <abc-image
                                :width="48"
                                :height="48"
                                :src="WpCodeLogo.MT"
                                alt="美团"
                            ></abc-image>
                            <abc-text size="largex" bold>
                                请登录美团网店授权
                            </abc-text>
                        </abc-flex>
                        <abc-button size="large" :loading="btnLoading" @click="authMeituan">
                            登录美团
                        </abc-button>
                    </template>
                </abc-flex>
                <abc-flex
                    v-else-if="crawlerLoading"
                    vertical
                    style="width: 100%; height: 100%;"
                    justify="center"
                    align="center"
                    :gap="40"
                >
                    <abc-loading-spinner middle></abc-loading-spinner>
                    <abc-text size="large">
                        正在获取商品数据...
                    </abc-text>
                </abc-flex>
                <template v-else>
                    <abc-content-empty v-if="crawlerFailed" value="获取商品数据失败，请重试">
                        <abc-space style="margin-top: 40px;">
                            <abc-button
                                :loading="btnLoading"
                                @click="handleSyncProductSummary"
                            >
                                重新获取
                            </abc-button>
                            <abc-button
                                :loading="btnLoading"
                                variant="ghost"
                                theme="danger"
                                @click="cancelAccess"
                            >
                                取消接入
                            </abc-button>
                        </abc-space>
                    </abc-content-empty>
                    <abc-flex
                        v-else-if="isSyncing"
                        vertical
                        align="center"
                        justify="center"
                        :gap="40"
                    >
                        <abc-flex
                            vertical
                            :gap="24"
                        >
                            <abc-flex vertical align="center" gap="large">
                                <abc-progress
                                    :percentage="syncInfo.progress"
                                    :custom-percentage="`${syncInfo.progress}%`"
                                    variant="circle"
                                    theme="green"
                                >
                                </abc-progress>
                                <div style="height: 46px;">
                                    <abc-flex
                                        v-if="syncInfo.progress === 100"
                                        vertical
                                        align="center"
                                        gap="middle"
                                    >
                                        <abc-text theme="success">
                                            网店接入成功
                                        </abc-text>
                                        <abc-text v-if="syncInfo.synced === syncInfo.binded" size="mini">
                                            网店商品已同步并绑定ABC商品，开始使用吧~
                                        </abc-text>
                                        <abc-text v-else>
                                            网店商品同步完成，{{ syncInfo.synced - syncInfo.binded }} 个商品无法自动绑定, 可
                                            <abc-link @click="handleToBind">
                                                去「商品管理」绑定
                                            </abc-link>
                                            或 订单出库时手动绑定
                                        </abc-text>
                                    </abc-flex>
                                    <abc-text v-else theme="success" bold>
                                        正在同步网店商品、自动绑定ABC商品
                                    </abc-text>
                                </div>
                            </abc-flex>
                            <abc-flex>
                                <abc-flex
                                    vertical
                                    align="center"
                                    gap="middle"
                                    style="width: 182px;"
                                >
                                    <abc-text theme="gray">
                                        网店商品总数
                                    </abc-text>
                                    <abc-text size="large">
                                        {{ syncInfo.total }}
                                    </abc-text>
                                </abc-flex>

                                <abc-row
                                    :gutter="[0, 'small']"
                                    :wrap="true"
                                    align="center"
                                    style="width: 182px;"
                                >
                                    <abc-col :span="11" style="text-align: right;">
                                        <abc-text theme="gray">
                                            已同步
                                        </abc-text>
                                    </abc-col>
                                    <abc-col :span="2">
                                        <abc-text theme="gray">
                                            ｜
                                        </abc-text>
                                    </abc-col>
                                    <abc-col :span="11">
                                        <abc-text theme="gray">
                                            已绑定
                                        </abc-text>
                                    </abc-col>

                                    <abc-col :span="11" style="text-align: right;">
                                        <abc-text size="large">
                                            {{ syncInfo.synced }}
                                        </abc-text>
                                    </abc-col>
                                    <abc-col :span="2">
                                        <abc-text size="large">
                                            ｜
                                        </abc-text>
                                    </abc-col>
                                    <abc-col :span="11">
                                        <abc-text size="large" :theme="syncInfo.progress === 100 && syncInfo.binded !== syncInfo.synced ? 'warning' : 'success'">
                                            {{ syncInfo.binded }}
                                        </abc-text>
                                    </abc-col>
                                </abc-row>
                                <abc-flex
                                    vertical
                                    align="center"
                                    gap="middle"
                                    style="width: 182px;"
                                >
                                    <abc-text theme="gray">
                                        待同步
                                    </abc-text>
                                    <abc-text size="large">
                                        {{ waitSync }}
                                    </abc-text>
                                </abc-flex>
                                <abc-flex
                                    vertical
                                    align="center"
                                    gap="middle"
                                    style="width: 182px;"
                                >
                                    <abc-text theme="gray">
                                        剩余时长
                                    </abc-text>
                                    <abc-text size="large">
                                        {{ syncInfo.progress === 100 ? '-' : syncTime }}
                                    </abc-text>
                                </abc-flex>
                            </abc-flex>
                        </abc-flex>
                        <abc-button
                            v-if="syncInfo.progress === 100"
                            :loading="btnLoading"
                            size="large"
                            theme="success"
                            @click="handleFinish"
                        >
                            完成
                        </abc-button>
                        <abc-space v-else :size="12">
                            <abc-button
                                :loading="btnLoading"
                                size="large"
                                variant="ghost"
                                @click="handleLeave"
                            >
                                暂时离开
                            </abc-button>
                            <abc-button
                                :loading="btnLoading"
                                size="large"
                                variant="ghost"
                                theme="danger"
                                @click="cancelAccess"
                            >
                                取消接入
                            </abc-button>
                        </abc-space>
                    </abc-flex>
                    <abc-flex
                        v-else
                        vertical
                        align="center"
                        justify="center"
                        gap="large"
                    >
                        <div v-if="bindClinicInfo.accountInfo.headImgUrl" style="position: relative;">
                            <abc-image
                                :src="logoIconMeiTuan"
                                :width="32"
                                :height="21"
                                style="position: absolute; top: -4px; left: -12px; z-index: 1;"
                            ></abc-image>
                            <abc-image
                                :src="bindClinicInfo.accountInfo.headImgUrl"
                                :width="48"
                                :height="48"
                                style="border: 1px solid var(--abc-color-P6); border-radius: 8px; object-fit: cover;"
                            ></abc-image>
                        </div>
                        <abc-image
                            v-else
                            :width="48"
                            :height="48"
                            :src="WpCodeLogo.MT"
                            alt="美团"
                        ></abc-image>
                        <abc-text size="largex" bold>
                            {{ syncInfo.total ? `网店已有 ${syncInfo.total} 个商品` : '网店暂无商品' }}
                        </abc-text>
                        <abc-text>
                            若网店商品不足，请前往美团网店创建
                            <abc-link>
                                查看教程
                            </abc-link>
                        </abc-text>

                        <abc-space :size="12" style="margin-top: 24px;">
                            <abc-button
                                v-if="syncInfo.total"
                                :loading="btnLoading"
                                size="large"
                                @click="handleSyncProduct"
                            >
                                同步商品
                            </abc-button>
                            <abc-button
                                v-else
                                :loading="btnLoading"
                                size="large"
                                variant="ghost"
                                @click="handleLeave"
                            >
                                继续接入，暂不同步商品
                            </abc-button>
                            <abc-button
                                :loading="btnLoading"
                                size="large"
                                variant="ghost"
                                theme="danger"
                                @click="cancelAccess"
                            >
                                取消接入
                            </abc-button>
                        </abc-space>
                    </abc-flex>
                </template>
            </template>
        </abc-flex>
    </abc-modal>
</template>

<script>
    import {
        EcShopTypeEnum, ECTypeEnum, WpCodeLogo,
    } from '../../utils/constants';
    import { MeituanAuthManager } from '@/daemon/crawler/provider/meituan';
    import logoIconMeiTuan from '@/assets/images/takeaway/meituan.png';
    import { formatDate } from '@abc/utils-date';
    import ECAuthAPI from '@/api/auth';
    import { OrderCloudDaemonService } from '@/daemon';
    import {
        MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE,
        MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED,
        MEITUAN_EVENT_SYNC_PRODUCT_INFO,
        MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE,
        MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED,
    } from '@/daemon/crawler/provider/meituan/constants';
    import { AuthStatus } from '@/daemon/crawler/common/constants';
    import OrderCloudStore from '@/daemon/crawler/store';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import { PharmacyOrderCloudRouterNameKeys } from '@/core/routes';

    export default {
        name: 'DialogEcBind',
        components: {
            AbcLoadingSpinner,
        },
        props: {
            detail: {
                type: Object,
                default: () => {},
            },
            bindClinic: {
                type: Object,
                default: null,
            },
            active: {
                type: Number,
                default: 0,
            },
            bindInfo: {
                type: Object,
                default: null,
            },
            isBind: {
                type: Boolean,
                default: false,
            },
            closeDialog: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                logoIconMeiTuan,
                WpCodeLogo,
                currentActive: 0,
                visible: false,
                btnLoading: false,
                isSyncing: false,
                crawlerLoading: false,
                crawlerFailed: false,
                readyLoading: false,
                meituanService: null,
                syncInfo: {
                    total: 0,
                    synced: 0,
                    binded: 0,
                    progress: 0,
                },
                ecMallId: '',
                bindClinicInfo: null,
            };
        },
        computed: {
            // 待同步
            waitSync() {
                return this.syncInfo.total - this.syncInfo.synced;
            },
            // 剩余同步时长
            syncTime() {
                const totalSeconds = Math.round(this.waitSync * 5);
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                let result = '';
                if (hours > 0) result += `${hours}小时`;
                if (minutes > 0) result += `${minutes}分`;
                result += `${seconds}秒`;
                return result;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        async created() {
            this.currentActive = this.active;
            this.bindClinicInfo = this.bindInfo;
            this.ecMallId = this.bindInfo?.ecMallId || '';
            if (this.isBind) {
                this.readyLoading = true;
                await new OrderCloudStore().fetchBindAuthList();
                await this.initCrawler();
                this.readyLoading = false;
                if (this.meituanService && this.syncInfo.total === 0) {
                    this.crawlerLoading = true;
                    await this.meituanService.scrapeProductsSummaryInfo();
                }
            }
        },
        methods: {
            async initCrawler() {
                try {
                    const crawlerManager = OrderCloudDaemonService.getInstance().getCrawlerManager();
                    if (crawlerManager) {
                        const timeoutPromise = new Promise((_, reject) => {
                            // eslint-disable-next-line abc/no-timer-id
                            setTimeout(() => reject(new Error('操作超时')), 30000);
                        });
                        const authMallList = await Promise.race([crawlerManager.getAuthMallList(), timeoutPromise]);
                        const MTMall = authMallList.find((mall) => mall.shopType === EcShopTypeEnum.O2O && mall.ecType === ECTypeEnum.MT);
                        this.meituanService = crawlerManager.getTargetService(MTMall.extMallId);
                        this.meituanService.on('event', (event) => {
                            if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_DONE) {
                                this.btnLoading = false;
                                this.syncInfo = this.meituanService.syncInfo;
                                this._needTips = false;
                            }
                            if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_IS_FAILED) {
                                this.btnLoading = false;
                                if (!this._needTips) return;
                                // 显示失败提示
                                this.$alert({
                                    type: 'error',
                                    title: '同步商品失败',
                                    content: '同步商品失败，可以重新尝试',
                                });
                                this._needTips = false;
                            }
                            // 同步进度
                            if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_INFO) {
                                this.syncInfo = this.meituanService.syncInfo;
                            }
                            if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_DONE) {
                                this.syncInfo = this.meituanService.syncInfo;
                                this.crawlerFailed = false;
                                this.crawlerLoading = false;
                            }
                            if (event.type === MEITUAN_EVENT_SYNC_PRODUCT_SUMMARY_IS_FAILED) {
                                // 显示失败提示
                                this.$alert({
                                    type: 'error',
                                    title: '获取商品汇总信息失败',
                                    content: '获取商品汇总信息失败，可以重新尝试',
                                });
                                this.crawlerFailed = true;
                                this.crawlerLoading = false;
                            }
                        });

                        const { authStatus } = this.meituanService;
                        if (authStatus !== AuthStatus.AUTHED) return;
                        // 检查是否正在同步商品
                        this.syncInfo = this.meituanService.syncInfo;
                        this.isSyncing = this.meituanService.isSyncingProduct;
                        this.crawlerLoading = this.meituanService.isSyncingProductSummary;
                    }
                } catch (error) {
                    console.error(error);
                    this.crawlerFailed = true;
                    this.crawlerLoading = false;
                }
            },
            handleClose() {
                this.$emit('close');
            },
            async authMeituan() {
                this.btnLoading = true;
                const item = this.detail;
                // 传递回调函数，当所有美团浏览器实例关闭时关闭对话框
                const response = await MeituanAuthManager.getInstance().requestBind(item.extMallId, () => {
                    // 当所有美团浏览器实例关闭时，关闭对话框
                    this.btnLoading = false;
                    this.closeDialog();
                });
                console.log('bind response', response);
                // const response = {
                //     status: true,
                //     data: {
                //         'cookies': [
                //             {
                //                 'name': 'WEBDFPID',
                //                 'value': '3472957w0y3u58740637u7x42z3x3u4u802z9y7v59w9795852739684-1750834296302-1750747767139QCYIQQUfd79fef3d01d5e9aadc18ccd4d0c95073854',
                //                 'domain': '.meituan.com',
                //                 'path': '/',
                //                 'expires': 1785307896.53826,
                //                 'size': 135,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'pharmacistAccount',
                //                 'value': '0',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.48876,
                //                 'size': 18,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'com.sankuai.yiyao.shangjia.main_strategy',
                //                 'value': '',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': -1,
                //                 'size': 40,
                //                 'httpOnly': false,
                //                 'secure': true,
                //                 'session': true,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': '_lxsdk_s',
                //                 'value': '197a0b2e302-dc2-4b2-cb%7C%7C10',
                //                 'domain': '.meituan.com',
                //                 'path': '/',
                //                 'expires': 1750749700,
                //                 'size': 38,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'poi_second_category_id',
                //                 'value': '179',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936569,
                //                 'size': 25,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'poi_first_category_id',
                //                 'value': '22',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936541,
                //                 'size': 23,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'account_second_type',
                //                 'value': '200',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936509,
                //                 'size': 22,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'acct_id',
                //                 'value': '*********',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936416,
                //                 'size': 16,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'single_poi_businesstype',
                //                 'value': '1',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.77211,
                //                 'size': 24,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'account_businesstype',
                //                 'value': '1',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.772081,
                //                 'size': 21,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'provinceId',
                //                 'value': '510000',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.771997,
                //                 'size': 16,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'city_location_id',
                //                 'value': '330200',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.772021,
                //                 'size': 22,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'brandId',
                //                 'value': '-1',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.17622,
                //                 'size': 9,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'cityId',
                //                 'value': '510100',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.771967,
                //                 'size': 12,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'terminal',
                //                 'value': 'bizCenter',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': -1,
                //                 'size': 17,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': true,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'accountAllPoiBusinessType',
                //                 'value': '1',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.772137,
                //                 'size': 26,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'logistics_support',
                //                 'value': '1',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.771921,
                //                 'size': 18,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'yy-epassport-accessToken',
                //                 'value': '1237coG1bsN5xlmT8N9cEejCj_PIvLpGUnOP5yVrxsSNeGmKNdpO6LQPdTAlwFsO5_bK7DB_Rpljf5xFkga6xg',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': -1,
                //                 'size': 110,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': true,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': '_gw_ab_call_37616_135',
                //                 'value': 'TRUE',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.492036,
                //                 'size': 25,
                //                 'httpOnly': true,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'region_version',
                //                 'value': '0',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176438,
                //                 'size': 15,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'ignore_set_router_proxy',
                //                 'value': 'false',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176389,
                //                 'size': 28,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'existBrandPoi',
                //                 'value': 'true',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176363,
                //                 'size': 17,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'poi_id',
                //                 'value': '26102708',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936479,
                //                 'size': 14,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'region_id',
                //                 'value': '0',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176415,
                //                 'size': 10,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'isChain',
                //                 'value': '0',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176342,
                //                 'size': 8,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'cacheTimeMark',
                //                 'value': '2025-06-24',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1750834297,
                //                 'size': 23,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'pushToken',
                //                 'value': '05ahtULs1TfJLueRSNyP6LPmKdttaooMEwdEbWnfVmY4*',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1785307895.939079,
                //                 'size': 54,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'token',
                //                 'value': '05ahtULs1TfJLueRSNyP6LPmKdttaooMEwdEbWnfVmY4*',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176195,
                //                 'size': 50,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'shopCategory',
                //                 'value': 'medicine',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1750751493.176317,
                //                 'size': 20,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'device_uuid',
                //                 'value': '!03fab2b9-e7fd-4e25-b76e-45b54168a1b4',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1785307893.176614,
                //                 'size': 48,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'isOfflineSelfOpen',
                //                 'value': '2',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176266,
                //                 'size': 18,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'set_info',
                //                 'value': '{"wmPoiId":26102708,"region_id":"1000330200","region_version":1734500101}',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339898,
                //                 'size': 81,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'bsid',
                //                 'value': '1237coG1bsN5xlmT8N9cEejCj_PIvLpGUnOP5yVrxsSNeGmKNdpO6LQPdTAlwFsO5_bK7DB_Rpljf5xFkga6xg',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176559,
                //                 'size': 90,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'newCategory',
                //                 'value': 'true',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.488718,
                //                 'size': 15,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'wmPoiId',
                //                 'value': '26102708',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176242,
                //                 'size': 15,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'acctId',
                //                 'value': '*********',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176169,
                //                 'size': 15,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'acct_name',
                //                 'value': 'mt353783qf',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.936452,
                //                 'size': 19,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'uuid_update',
                //                 'value': 'true',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1785307893.175981,
                //                 'size': 15,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'wpush_server_url',
                //                 'value': 'wss://wpush.meituan.com',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': -1,
                //                 'size': 39,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': true,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': '_gw_ab_37616_135',
                //                 'value': '759',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.491995,
                //                 'size': 19,
                //                 'httpOnly': true,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'city_id',
                //                 'value': '0',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': 1753339893.176294,
                //                 'size': 8,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': '_lxsdk',
                //                 'value': '197a0b2e301c8-015ffcfd573112-47104a5c-1fa400-197a0b2e301a5',
                //                 'domain': '.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.556487,
                //                 'size': 64,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': 'location_id',
                //                 'value': '330282',
                //                 'domain': 'yiyao.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.77205,
                //                 'size': 17,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //             {
                //                 'name': '_lxsdk_cuid',
                //                 'value': '197a0b2e301c8-015ffcfd573112-47104a5c-1fa400-197a0b2e301a5',
                //                 'domain': '.meituan.com',
                //                 'path': '/',
                //                 'expires': **********.554007,
                //                 'size': 69,
                //                 'httpOnly': false,
                //                 'secure': false,
                //                 'session': false,
                //                 'sameParty': false,
                //                 'sourceScheme': 'Secure',
                //                 'sourcePort': 443,
                //             },
                //         ],
                //         'accountInfo': {
                //             'id': 'mt353783qf',
                //             'accountName': 'mt353783qf',
                //             'mobile': '***********',
                //             'wmPoiName': '慈捷大药房',
                //             'wmPoiStatus': 3,
                //             'wmPoiStatusDesc': '停业中',
                //             'headImgUrl': 'http://p0.meituan.net/qua/b9d745bd357f7dc98f6e4e4bb2bfb18f21078.png',
                //         },
                //     },
                // };
                if (response.status === false) {
                    const errorMessage = response.data?.error?.message || response.message || '未知错误';
                    if (errorMessage.indexOf('Navigation') === -1) {
                        // 显示错误信息
                        this.$alert({
                            type: 'warn',
                            title: '登录失败',
                            content: errorMessage,
                        });
                    }
                } else {
                    this.bindClinicInfo = response.data;
                }
                this.btnLoading = false;
            },
            async handleBind() {
                if (this.btnLoading) return;
                try {
                    this.btnLoading = true;
                    const {
                        cookies,
                        accountInfo,
                    } = this.bindClinicInfo;
                    const tokenCookie = cookies.find((it) => it.name === 'token');
                    // 调用ECAuthAPI.authEc
                    const authEcParams = {
                        authCode: this.authCode,
                        ecId: accountInfo.id,
                        ecAccessToken: tokenCookie.value,
                        ecAccessTokenExpireTime: formatDate(tokenCookie.expires * 1000, 'YYYY-MM-DD HH:mm:ss'),
                        clientCookie: JSON.stringify(cookies),

                        ecMallInfo: {
                            mallDesc: accountInfo.mallDesc,
                            mallId: accountInfo.id,
                            mallLogo: accountInfo.headImgUrl,
                            mallName: accountInfo.wmPoiName,
                            shopType: EcShopTypeEnum.O2O,
                        },
                        ecType: ECTypeEnum.MT, // 美团的ecType
                    };

                    const authEcResponse = await ECAuthAPI.authEc(authEcParams);
                    if (authEcResponse) {
                        // 调用authBind
                        this.ecMallId = authEcResponse.ecMallId;
                        const authBindParams = {
                            bindClinicId: this.bindClinic.clinicId,
                            ecMallId: authEcResponse.ecMallId,
                            ecType: authEcResponse.ecType,
                        };
                        await ECAuthAPI.authBind(authBindParams);

                        // 保存映射关系
                        MeituanAuthManager.getInstance().addAccount(accountInfo.id, {
                            accountInfo,
                            cookies,
                            clinicId: this.bindClinic.clinicId,
                        });

                        await this.handleSyncProductSummary();

                        this.$abcEventBus.$emit('ec-auth-success');
                        this.currentActive = 1;
                    }
                } catch (error) {
                    console.error('Auth EC or Bind failed:', error);
                } finally {
                    this.btnLoading = false;
                }
            },
            async handleSyncProductSummary() {
                await new OrderCloudStore().fetchBindAuthList();
                await this.initCrawler();
                await this.meituanService.scrapeProductsSummaryInfo();
                this.crawlerFailed = false;
                this.crawlerLoading = true;
            },
            async handleSyncProduct() {
                if (this.btnLoading) return;

                this.btnLoading = true;
                try {
                    if (!this.meituanService) {
                        this.$alert({
                            type: 'error',
                            title: '提示',
                            content: '需要使用ABC客户端',
                        });
                        return;
                    }

                    // 开始同步商品
                    await this.meituanService.scrapeProductListOnce();
                    this.isSyncing = true;
                    this.btnLoading = false;

                    this._needTips = true;
                } catch (error) {
                    console.error('同步商品失败', error);
                    this.$alert({
                        type: 'error',
                        title: '同步商品失败',
                        content: '同步商品失败，可以重新尝试',
                    });
                }
            },
            handleLeave() {
                this.visible = false;
            },
            async cancelAccess() {
                this.btnLoading = true;
                try {
                    this.$confirm({
                        type: 'warn',
                        size: 'small',
                        title: '确认取消接入',
                        content: '取消接入网店后将解除网店授权并清除已同步商品，确认取消吗？',
                        closeAfterConfirm: false,
                        onConfirm: async (modelVm) => {
                            modelVm.loading = true;
                            await ECAuthAPI.cancelMTAccess(this.ecMallId);
                            if (this.isSyncing) {
                                this.meituanService.destroy();
                            }
                            this.$abcEventBus.$emit('ec-auth-success');
                            this.visible = false;
                            modelVm.loading = false;
                            modelVm.close();
                        },
                        onCancel: () => {
                            console.log('cancel');
                        },
                    });
                } catch (e) {
                    console.log(e);
                } finally {
                    this.btnLoading = false;

                }
            },
            async handleFinish() {
                this.btnLoading = true;
                const { data } = await ECAuthAPI.completeMTAccess(this.ecMallId);
                this.btnLoading = false;
                if (data.isSuccess) {
                    this.visible = false;
                    this.$abcEventBus.$emit('ec-auth-success');
                } else {
                    this.$alert({
                        type: 'error',
                        title: '提示',
                        content: data,
                    });
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleToBind() {
                this.visible = false;
                this.$router.push({
                    name: PharmacyOrderCloudRouterNameKeys.takeawayMtGoods,
                });
            },
        },
    };
</script>
