<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="分配ABC商品库存"
        :auto-focus="false"
        size="huge"
        append-to-body
        content-styles="height: 618px"
        class="allocation-stock-dialog"
    >
        <!-- 商品信息卡片 -->
        <abc-card
            radius-size="small"
            padding-size="large"
            background="gray"
            border
            style="height: 88px;"
        >
            <abc-flex v-if="hisGoodsInfo" justify="space-between" align="flex-start">
                <abc-flex vertical gap="2">
                    <abc-text size="large" bold theme="black">
                        {{ hisGoodsInfo.displayName }}
                    </abc-text>
                    <abc-text size="normal" theme="gray">
                        {{ hisGoodsInfo.displaySpec }} {{ hisGoodsInfo.manufacturer }} {{ hisGoodsInfo.shortId }}
                    </abc-text>
                </abc-flex>
                <abc-space size="small">
                    <abc-text size="large" theme="gray">
                        ABC可售库存
                    </abc-text>
                    <abc-text size="large" bold theme="black">
                        {{ hisGoodsInfo.dispStockGoodsCount }}
                    </abc-text>
                </abc-space>
            </abc-flex>
        </abc-card>

        <!-- 分配方式 -->
        <abc-flex style="margin-top: 24px;">
            <abc-text size="normal" theme="gray" style="width: 96px;">
                分配方式
            </abc-text>
            <abc-flex style="flex: 1;">
                <abc-radio-group v-model="allocationMode" item-block @change="handleMode">
                    <abc-radio :label="0">
                        分配真实库存
                        <abc-tooltip-info content="以ABC商品的真实可售库存为基数分配" placement="right" expand></abc-tooltip-info>
                    </abc-radio>
                    <abc-radio-group
                        v-if="allocationMode === 0"
                        v-model="assignStockType"
                        item-block
                        style="margin-left: 24px;"
                    >
                        <abc-radio :label="AllocationStockType.RATIO" style="margin-top: 12px;">
                            按比例
                        </abc-radio>
                        <abc-text theme="gray">
                            按「比例」分配库存，库存变化自动更新，库存余数分配给销量最高的商品
                        </abc-text>
                        <abc-radio :label="AllocationStockType.SHARE" style="margin-top: 12px;">
                            共享库存
                        </abc-radio>
                        <abc-text theme="gray">
                            相关网店商品「共用总库存」，任一商品售出后实时扣减总库存并更新其他商品库存
                        </abc-text>
                    </abc-radio-group>
                    <abc-radio :label="AllocationStockType.CUSTOM" style="margin-top: 12px;">
                        自定义虚拟库存
                        <abc-tooltip-info content="分配给网店商品任意库存，售出不影响真实库存，售完手动补货" placement="right" expand></abc-tooltip-info>
                    </abc-radio>
                </abc-radio-group>
            </abc-flex>
        </abc-flex>

        <!-- 分配库存表格 -->
        <abc-flex style="margin-top: 24px;">
            <abc-text size="normal" theme="gray" style="width: 96px;">
                分配库存
            </abc-text>
            <abc-form ref="abcForm" is-excel item-no-margin>
                <abc-table
                    :type="isShareStock ? 'normal' : 'excel'"
                    style="width: 816px;"
                    cell-size="xlarge"
                    :table-min-height="262"
                    :render-config="renderConfig"
                    :data-list="relatedGoodsSku"
                    :need-selected="!isShareStock"
                    @handleClickTr="handleClickTr"
                >
                    <template #platform="{ trData: item }">
                        <abc-table-cell>
                            {{ ECTypeText[item.ecType] }}
                        </abc-table-cell>
                    </template>
                    <template #goodsSkuName="{ trData: item }">
                        <abc-table-cell>
                            <div class="ellipsis-2" :title="item.goodsSkuName">
                                {{ item.goodsSkuName }}
                            </div>
                        </abc-table-cell>
                    </template>
                    <template #shareStock>
                        <abc-table-cell>
                            {{ hisGoodsInfo.dispStockGoodsCount }}
                        </abc-table-cell>
                    </template>
                    <template #customStock="{ trData: item }">
                        <abc-form-item required>
                            <abc-input
                                v-model="item.customCount"
                                v-abc-focus-selected
                                type="number"
                                :input-custom-style="{ textAlign: 'center' }"
                                :config="{
                                    max: 9999,
                                    min: 0,
                                    supportZero: true
                                }"
                            >
                                <template #appendInner>
                                    {{ item.useDismounting ? pieceUnit : packageUnit }}
                                </template>
                            </abc-input>
                        </abc-form-item>
                    </template>
                    <template #assignRatio="{ trData: item }">
                        <abc-form-item :validate-event="validateRatio">
                            <abc-input
                                v-model="item.assignStockRatio"
                                v-abc-focus-selected
                                type="number"
                                :input-custom-style="{ textAlign: 'center' }"
                                :config="{
                                    max: 100,
                                    min: 0,
                                }"
                            >
                                <template #appendInner>
                                    %
                                </template>
                            </abc-input>
                        </abc-form-item>
                    </template>
                    <template #assignStock="{ trData: item }">
                        <abc-table-cell>
                            {{ getAssignStock(item) }}{{ item.useDismounting ? pieceUnit : packageUnit }}
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-form>
        </abc-flex>

        <!-- footer -->
        <template #footer>
            <abc-flex flex="1" justify="flex-end">
                <abc-button :disabled="!isUpdated" :loading="btnLoading" @click="onConfirm">
                    确定
                </abc-button>
                <abc-button variant="ghost" @click="onCancel">
                    取消
                </abc-button>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script type="text/babel">
    import ECGoodsAPI from '@/api/goods';
    import {
        ECTypeText, AllocationStockType,
    } from '@/utils/constants.js';
    import { isEqual } from 'MfBase/lodash';
    import { clone } from '@abc/utils';

    export default {
        name: 'DialogAllocationStock',
        props: {
            value: Boolean,
            hisGoodsId: {
                type: String,
                required: true,
            },
            clinicId: {
                type: String,
                required: true,
            },
            isBindGoods: {
                type: Boolean,
                default: false,
            },
            defaultList: {
                type: Array,
                default: () => [],
            },
            currentItem: {
                type: Object,
            },
            ecType: {
                type: Number,
            },
            onChange: Function,
        },
        data() {
            return {
                ECTypeText,
                AllocationStockType,
                allocationMode: 0,
                assignStockType: 0,
                visible: false,
                btnLoading: false,

                hisGoodsInfo: {},
                relatedGoodsSku: [],
            };
        },
        computed: {
            packageUnit() {
                return this.hisGoodsInfo?.packageUnit;
            },
            pieceUnit() {
                return this.hisGoodsInfo?.pieceUnit;
            },
            renderConfig() {
                const list = [
                    {
                        'key': 'platform',
                        'label': '平台',
                        'style': {
                            'min-width': '66px',
                            'max-width': '66px',
                        },
                    },
                    {
                        'key': 'mallName',
                        'label': '网店',
                        'style': {
                            'min-width': '150px',
                            'max-width': '150px',
                        },
                    },
                    {
                        'key': 'goodsSkuName',
                        'label': '商品',
                        'style': {
                            'flex': '1',
                        },
                    },
                ];
                let operateList = [];
                if (this.allocationMode === AllocationStockType.CUSTOM) {
                    operateList = operateList.concat([
                        {
                            'key': 'customStock',
                            'label': '自定义库存',
                            'style': {
                                'min-width': '120px',
                                'max-width': '120px',
                            },
                        },
                    ]);
                } else if (this.assignStockType === AllocationStockType.RATIO) {
                    operateList = operateList.concat([
                        {
                            'key': 'assignRatio',
                            'label': '分配库存比例',
                            'style': {
                                'min-width': '100px',
                                'max-width': '100px',
                            },
                        },
                        {
                            'key': 'assignStock',
                            'label': '分配库存',
                            'style': {
                                'min-width': '120px',
                                'max-width': '120px',
                            },
                        },
                    ]);
                } else if (this.assignStockType === AllocationStockType.SHARE) {
                    operateList = operateList.concat([
                        {
                            'key': 'shareStock',
                            'label': '可售库存',
                            'style': {
                                'min-width': '120px',
                                'max-width': '120px',
                            },
                        },
                    ]);
                }
                return {
                    hasInnerBorder: true,
                    list: list.concat(operateList),
                };
            },

            isUpdated() {
                if (this.defaultList?.length) return true;
                return !isEqual(this._cache, {
                    allocationMode: this.allocationMode,
                    assignStockType: this.assignStockType,
                    relatedGoodsSku: this.relatedGoodsSku,
                });
            },

            isShareStock() {
                return this.allocationMode === 0 && this.assignStockType === AllocationStockType.SHARE;
            },
        },
        watch: {
            allocationMode: {
                handler(value) {
                    if (value === 0 && this.assignStockType === AllocationStockType.SHARE) return;
                    this.focusTableFirstInput();
                },
                immediate: true,
            },
            assignStockType(value) {
                if (value === AllocationStockType.RATIO) {
                    this.focusTableFirstInput();
                }
            },
        },
        created() {
            this.fetchRelatedSku();
        },
        methods: {
            handleMode() {
                if (this.allocationMode === AllocationStockType.CUSTOM) {
                    this.relatedGoodsSku.forEach((item) => {
                        item.customCount = null;
                    });
                } else {
                    this.relatedGoodsSku.forEach((item) => {
                        item.customCount = item.useDismounting ?
                            item.assignedStockPieceCount :
                            item.assignedStockPackageCount;
                    });
                }
            },
            validateRatio(val, callback) {
                const totalRatio = this.relatedGoodsSku.reduce((acc, cur) => {
                    acc += +(cur.assignStockRatio || 0);
                    return acc;
                }, 0);
                if (totalRatio > 100) {
                    callback({
                        validate: false, message: '超出ABC可售库存',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            getAssignStock(item) {
                const {
                    useDismounting,
                    assignStockRatio,
                } = item;
                if (!assignStockRatio) return 0;
                const {
                    stockPackageCount,
                    stockPieceCount = 0,
                    pieceNum = 1,
                } = this.hisGoodsInfo || {};
                const totalPieceStock = stockPieceCount + stockPackageCount * pieceNum;
                const stock = totalPieceStock * assignStockRatio / 100;
                if (useDismounting) {
                    return Math.floor(stock);
                }
                return Math.floor(stock / pieceNum);
            },
            async fetchRelatedSku() {
                const { data } = await ECGoodsAPI.fetchRelatedSku(this.hisGoodsId, {
                    clinicId: this.clinicId,
                });
                this.hisGoodsInfo = data.hisGoodsInfo || {};
                this.relatedGoodsSku = (data.relatedGoodsSku || []);
                if (this.relatedGoodsSku.length) {
                    const {
                        assignStockType,
                    } = this.relatedGoodsSku[0];
                    if (assignStockType === AllocationStockType.CUSTOM) {
                        this.allocationMode = AllocationStockType.CUSTOM;
                    } else {
                        this.allocationMode = 0;
                        this.assignStockType = assignStockType;
                    }
                }

                this.relatedGoodsSku.sort((a, b) => {
                    // 当前商品排最前面
                    if (a.id === this.currentItem?.id) return -1;
                    if (b.id === this.currentItem?.id) return 1;

                    // 根据 ecType 是否和 this.ecType 相同，相同的排在前面
                    if (a.ecType === this.ecType && b.ecType !== this.ecType) {
                        return -1;
                    }
                    if (a.ecType !== this.ecType && b.ecType === this.ecType) {
                        return 1;
                    }
                    return 0;
                });
                this.relatedGoodsSku = (this.defaultList || []).concat(this.relatedGoodsSku);

                this.$nextTick(() => {
                    this.relatedGoodsSku = this.relatedGoodsSku.map((item) => {
                        if (this.currentItem?.id === item.id) {
                            item.useDismounting = this.currentItem.useDismounting;
                        }
                        this.$set(item, 'customCount', item.useDismounting ? item.assignedStockPieceCount : item.assignedStockPackageCount);

                        return item;
                    });
                    this._cache = {
                        allocationMode: this.allocationMode,
                        assignStockType: this.assignStockType,
                        relatedGoodsSku: clone(this.relatedGoodsSku),
                    };
                });

            },
            async onConfirm() {
                this.$refs.abcForm.validate((valid) => {
                    if (valid) {
                        this.submitHandler();
                    } else {
                        console.error('valid error');
                    }
                });
            },

            handleData(item) {
                const {
                    stockPackageCount,
                    stockPieceCount,
                } = this.hisGoodsInfo || {};
                const assignStockType = this.allocationMode === AllocationStockType.CUSTOM ?
                    AllocationStockType.CUSTOM :
                    this.assignStockType;

                const assignStockRatio = assignStockType === AllocationStockType.RATIO ?
                    item.assignStockRatio :
                    null;

                let assignedStockPieceCount = null;
                if (this.allocationMode === AllocationStockType.CUSTOM) {
                    assignedStockPieceCount = item.useDismounting ? item.customCount : null;
                } else if (assignStockType === AllocationStockType.SHARE) {
                    assignedStockPieceCount = stockPieceCount;
                } else if (assignStockType === AllocationStockType.RATIO) {
                    assignedStockPieceCount = item.useDismounting ? this.getAssignStock(item) : null;
                }

                let assignedStockPackageCount = null;
                if (this.allocationMode === AllocationStockType.CUSTOM) {
                    assignedStockPackageCount = item.useDismounting ? null : item.customCount;
                } else if (assignStockType === AllocationStockType.SHARE) {
                    assignedStockPackageCount = stockPackageCount;
                } else if (assignStockType === AllocationStockType.RATIO) {
                    assignedStockPackageCount = item.useDismounting ? null : this.getAssignStock(item);
                }

                return {
                    assignStockType,
                    assignStockRatio,
                    assignedStockPieceCount,
                    assignedStockPackageCount,
                };
            },
            async submitHandler() {
                const newItem = this.relatedGoodsSku.find((item) => !item.id);
                if (newItem) {
                    this.onChange && this.onChange([this.handleData(newItem)]);
                }
                const relatedGoodsSkuReqs = this.relatedGoodsSku.filter((it) => it.id).map((it) => {
                    return {
                        id: it.id,
                        ...this.handleData(it),
                    };
                });
                if (relatedGoodsSkuReqs.length === 0) {
                    this.closeHandler();
                    return;
                }
                try {

                    this.btnLoading = true;
                    await ECGoodsAPI.updateAssignedStock(this.hisGoodsId, {
                        clinicId: this.clinicId,
                        relatedGoodsSkuReqs,
                    });
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.onChange && this.onChange(relatedGoodsSkuReqs);
                    this.closeHandler();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            onCancel() {
                this.closeHandler();
            },
            closeHandler() {
                this.visible = false;
                this.destroyElement();
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            focusTableFirstInput() {
                this.$nextTick(() => {
                    // Find the first input element within the table
                    const firstInput = this.$el.querySelector('.abc-table-body input');
                    if (firstInput) {
                        firstInput.focus();
                        if (!this.currentClickTr || this.relatedGoodsSku.findIndex((item) => item.id === this.currentClickTr.id)) {
                            firstInput.click();
                        }
                    }
                });
            },
            handleClickTr(item) {
                this.currentClickTr = item;
            },
        },
    };
</script>
