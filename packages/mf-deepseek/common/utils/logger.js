function isObject(x) {
    return typeof x === 'object' && x !== null;
}

function assembleOTAttribute(scene, data) {
    if (!scene) {
        return null;
    }
    const result = {
        scene,
    };
    if (data == null) {
        return result;
    }
    // 如果data传的字符串，使用data作为key
    if (!isObject(data)) {
        data = {
            data,
        };
    }
    Object.keys(data).map((key) => {
        result[`${scene}.${key}`] = data[key];
    });
    return result;
}

function _send(data) {
    try {
        if (window.feLogger) {
            window.feLogger.send(data);
        } else {
            console.log('Logger: ', data);
        }
    } catch (e) {
        console.error('Logger.exception', e);
    }
}

export default class Logger {
    /**
     *  用于数据上报
     * @param scene {string} 场景值，用于区分上报场景
     * @param data {Any} 自定义数据
     */
    static report({
        scene,
        data,
    }) {
        return _send({
            scene,
            data,
        });
    }
    
    /**
     * 用于分析型数据上报
     * @param scene {string} 场景值，用于区分上报场景
     * @param data? {Any} 自定义数据
     */
    static reportAnalytics(scene, data) {
        const reportData = assembleOTAttribute(scene, data);
        if (!reportData) {
            return;
        }
        return _send(reportData);
    }
}


const guid = () => {
    return 'xxxxxxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

/**
 *
 * @param scene
 * @param context
 * @returns {{report: function(step: string, extraData?: {}): void}}
 */
export const createTraceLogger = (scene, context = {}) => {
    const traceId = guid();
    return {
        traceId,
        context,
        report: (step, extraData = {}) => {
            Logger.report({
                scene,
                data: {
                    traceId,
                    step,
                    ...context,
                    ...extraData,
                },
            });
        },
        reportAnalytics: (step, extraData = {}) => {
            Logger.reportAnalytics(scene, {
                traceId,
                step,
                ...context,
                ...extraData,
            });
        },
    };
};
