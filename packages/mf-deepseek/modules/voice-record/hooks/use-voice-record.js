import { defineStore } from 'MfBase/pinia';
import WebAudioSpeechRecognizer from '../asr/webaudiospeechrecognizer';
import config from '../config';
import { fetchTemporaryToken } from '../services/api';
import {
    ToastFunc as Toast,
    ModalFunc as AbcModal,
} from '@abc/ui-pc';
import { markRaw } from 'vue';
import { createTraceLogger } from '@/common/utils/logger';

/**
 * Pinia store for managing voice record state and logic
 */
export const useVoiceRecordStore = defineStore('voiceRecord', {
    state: () => ({
        bindOutpatientSheetId: null,
        state: 'initial',
        webAudioSpeechRecognizer: null,
        isRecording: false,
        waveformData: null,
        recordTimer: null,
        recordTime: 0,
        audioData: markRaw([]),
        completedSentences: [],
        currentSentence: '',
        currentStartTime: 0,
        currentEndTime: 0,
        micPermissionStatus: 'unknown',
        showPermissionTip: false,
        timeOffset: 0,
        isContinueRecording: false,
        finalResult: [],
        waitingAsrLastMessage: false,
        finishCallbacks: [],
        isStarting: false, // 正在启动
        isPausing: false, // 正在停止
        isStopping: false, // 正在停止
        logger: null,
        recognitionCompleteTimer: null,
    }),
    getters: {
        formattedTime(state) {
            const minutes = Math.floor(state.recordTime / 60).toString().padStart(2, '0');
            const seconds = (state.recordTime % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        },
        /**
         * 剩余时间，最多录制 10 分钟
         * @returns {number}
         */
        remainTime(state) {
            return Math.max(0, 60 * 15 - state.recordTime);
        },
        formattedRemainTime(state) {
            const minutes = Math.floor(state.remainTime / 60).toString().padStart(2, '0');
            const seconds = (state.remainTime % 60).toString().padStart(2, '0');
            return `${minutes}:${seconds}`;
        },
        fullSentence(state) {
            return state.completedSentences.map((s) => s.text).join('') + this.currentSentence;
        },
    },
    actions: {
        setState(state) {
            this.state = state;
            if (state === 'initial') {
                // 恢复到初始态，需要清空数据
                this.audioData = markRaw([]);
                this.completedSentences = [];
                this.currentSentence = '';
                this.currentStartTime = 0;
                this.currentEndTime = 0;
                this.timeOffset = 0;
                this.isContinueRecording = false;
                this.finalResult = [];
                this.waitingAsrLastMessage = false;
            }
        },
        close(cb) {
            if (typeof cb === 'function') cb('close');
            this.setState('initial');
        },
        requestMicPermission(resolve, reject) {
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then((stream) => {
                    stream.getTracks().forEach((track) => track.stop());
                    this.micPermissionStatus = 'granted';
                    this.showPermissionTip = false;
                    resolve(true);
                })
                .catch((e) => {
                    this.micPermissionStatus = 'denied';
                    this.showPermissionTip = true;
                    if (e?.name === 'NotFoundError') {
                        reject(new Error('请连接录音设备'));
                    } else if (e?.name === 'NotAllowedError') {
                        reject(new Error('请允许录音设备的使用权限，或者下载 ABC 客户端使用'));
                    } else {
                        reject(e);
                    }
                });
        },
        checkMicPermission() {
            return new Promise((resolve, reject) => {
                if (navigator.permissions && navigator.permissions.query) {
                    navigator.permissions.query({ name: 'microphone' })
                        .then((permissionStatus) => {
                            this.micPermissionStatus = permissionStatus.state;
                            permissionStatus.onchange = () => {
                                this.micPermissionStatus = permissionStatus.state;
                                if (permissionStatus.state === 'granted') {
                                    this.showPermissionTip = false;
                                }
                            };
                            if (permissionStatus.state === 'granted') {
                                resolve(true);
                            } else if (permissionStatus.state === 'prompt') {
                                this.requestMicPermission(resolve, reject);
                            } else {
                                this.showPermissionTip = true;
                                reject(new Error('请允许录音设备的使用权限，或者下载 ABC 客户端使用'));
                            }
                        })
                        .catch(() => {
                            this.requestMicPermission(resolve, reject);
                        });
                } else {
                    this.requestMicPermission(resolve, reject);
                }
            });
        },
        async startRecordingProcess() {
            const token = await fetchTemporaryToken();
            if (!token) {
                this.logger.report('获取临时凭证失败');
                // 可用回调或全局toast
                Toast.error('获取临时凭证失败');
                this.setState('initial');
                this.isRecording = false;
                return;
            }
            const params = {
                secretid: token.tmpSecretId,
                secretkey: token.tmpSecretKey,
                token: token.token,
                ...config,
                bizId: this.bindOutpatientSheetId,
            };
            this.webAudioSpeechRecognizer = new WebAudioSpeechRecognizer(params, false, this.logger);
            this.currentSentence = '';
            this.currentStartTime = 0;
            this.currentEndTime = 0;
            this.webAudioSpeechRecognizer.onWaveformUpdate = (data) => {
                this.waveformData = data;
            };
            this.webAudioSpeechRecognizer.OnRecognitionStart = () => {
                // 语音识别开始时上报
                this.logger.report('OnRecognitionStart');
            };
            const timeOffsetToUse = this.isContinueRecording ? this.timeOffset : 0;
            this.isContinueRecording = false;
            this.webAudioSpeechRecognizer.OnSentenceBegin = (res) => {
                this.currentSentence = '';
                if (res.result && res.result.voice_text_str) {
                    this.currentStartTime = (res.result.start_time || 0) + timeOffsetToUse;
                }
            };
            this.webAudioSpeechRecognizer.OnRecognitionResultChange = (res) => {
                if (res.result && res.result.voice_text_str) {
                    this.currentSentence = res.result.voice_text_str;
                    const originalStartTime = res.result.start_time || 0;
                    const originalEndTime = res.result.end_time || 0;
                    this.currentStartTime = originalStartTime + this.timeOffset;
                    this.currentEndTime = originalEndTime + this.timeOffset;
                }
            };
            this.webAudioSpeechRecognizer.OnSentenceEnd = (res) => {
                if (res.result && res.result.voice_text_str) {
                    const originalStartMs = res.result.start_time || 0;
                    const originalEndMs = res.result.end_time || 0;
                    const startMs = originalStartMs + this.timeOffset;
                    const endMs = originalEndMs + this.timeOffset;
                    const startSec = Math.floor(startMs / 1000);
                    const startMinutes = Math.floor(startSec / 60).toString().padStart(2, '0');
                    const startSeconds = (startSec % 60).toString().padStart(2, '0');
                    const timeStr = `${startMinutes}:${startSeconds}`;
                    const completedSentence = {
                        text: res.result.voice_text_str,
                        startTime: startMs,
                        endTime: endMs,
                        time: timeStr,
                        index: res.result.index,
                    };
                    this.completedSentences.push(completedSentence);
                    this.currentSentence = '';
                }
            };
            this.webAudioSpeechRecognizer.OnRecognitionComplete = () => {
                this.logger.report('OnRecognitionComplete');
                if (this.recognitionCompleteTimer) {
                    clearTimeout(this.recognitionCompleteTimer);
                    this.recognitionCompleteTimer = null;
                }
                if (this.waitingAsrLastMessage) {
                    this.processAsrResult();
                    this.waitingAsrLastMessage = false;
                }
            };
            this.webAudioSpeechRecognizer.OnError = (err) => {
                // 可用回调或全局toast
                console.log('webAudioSpeechRecognizer.onError', err);
                this.logger.report('OnRecognitionError', {
                    err,
                });
                this.stopRecording();
            };
            this.webAudioSpeechRecognizer.OnRecorderStop = (data) => {
                this.audioData = markRaw(this.audioData.concat(data));
            };
            await this.webAudioSpeechRecognizer.start();
            this.setState('recording');
            this.isRecording = true;
            this.recordTimer = setInterval(() => {
                this.recordTime++;
            }, 1000);
        },
        startRecording(outpatientSheetId) {
            if (this.isStarting || this.isPausing || this.isRecording) return;
            this.isStarting = true;
            if (outpatientSheetId) {
                this.bindOutpatientSheetId = outpatientSheetId;
                this.logger = createTraceLogger('voice-record-asr', {
                    outpatientSheetId,
                });

                this.logger.report('开始录音');
            }

            return this.checkMicPermission()
                .then(async () => {
                    await this.startRecordingProcess();
                })
                .catch((e) => {
                    console.error('录音权限检查失败', e);
                    // Toast.error(e?.message || '获取录音权限失败');
                    AbcModal.alert({
                        type: 'info',
                        title: '提示',
                        content: e?.message || '请允许录音设备的使用权限，或者下载 ABC 客户端使用',
                        closeAfterConfirm: true,
                        showClose: false,
                    });
                    this.showPermissionTip = true;
                    this.logger.report('录音权限检查失败', {
                        err: e?.message,
                    });
                }).finally(() => {
                    this.isStarting = false;
                });
        },
        async pauseRecording() {
            if (this.isStarting || this.isPausing) {
                return;
            }
            this.isPausing = true;
            window._vue.$abcPlatform.service.report.reportEventSLS('voice_record_pause_btn_clk', '点击暂停录音');
            // 上报录音暂停
            this.logger.report('暂停录音', {
                recordTime: this.recordTime,
            });
            if (this.webAudioSpeechRecognizer) {
                await this.webAudioSpeechRecognizer.stop();
                this.webAudioSpeechRecognizer.destroyStream();
                this.webAudioSpeechRecognizer = null;
                clearInterval(this.recordTimer);
                this.recordTimer = null;
                this.setState('paused');
                this.isRecording = false;
                this.waveformData = null;
                if (this.completedSentences.length > 0) {
                    const lastSentence = this.completedSentences[this.completedSentences.length - 1];
                    this.timeOffset = lastSentence.endTime;
                } else if (this.currentEndTime > 0) {
                    this.timeOffset = this.currentEndTime;
                }
            }
            this.isPausing = false;
        },
        async continueRecording() {
            this.isContinueRecording = true;
            window._vue.$abcPlatform.service.report.reportEventSLS('voice_record_resume_btn_clk', '点击继续录音');
            this.logger.report('继续录音', {
                recordTime: this.recordTime,
            });
            await this.startRecording();
        },
        resetRecording() {
            clearInterval(this.recordTimer);
            this.recordTimer = null;
            this.recordTime = 0;
            this.waveformData = null;
            this.isRecording = false;
            this.timeOffset = 0;
        },
        processAsrResult() {
            if (this.recognitionCompleteTimer) {
                clearTimeout(this.recognitionCompleteTimer);
                this.recognitionCompleteTimer = null;
            }
            this.finalResult = this.completedSentences;
            this.completedSentences = [];
            this.currentSentence = '';
            this.currentStartTime = 0;
            this.currentEndTime = 0;
            this.bindOutpatientSheetId = null;
            // 上报语音识别结果处理
            this.logger.report('processAsrResult', {
                recordTime: this.recordTime,
                finalResult: this.finalResult,
            });
            // 通过回调通知父组件生成
            for (const callback of this.finishCallbacks) {
                if (typeof callback === 'function') {
                    callback({
                        audioData: this.audioData,
                        asrResult: this.finalResult,
                        logger: this.logger,
                    });
                }
            }
            this.setState('initial');
        },
        async stopRecording(isNormalEnd) {
            if (this.isStopping) {
                return;
            }
            this.isStopping = true;
            // 上报录音结束
            this.logger.report('录音结束', {
                recordTime: this.recordTime,
                isNormalEnd,
            });
            if (isNormalEnd) {
                window._vue.$abcPlatform.service.report.reportEventSLS('voice_record_stop_btn_clk', '点击结束录音');
                if (this.state === 'paused') {
                    this.processAsrResult();
                } else {
                    this.waitingAsrLastMessage = true;
                    // 增加超时保护，以防 OnRecognitionComplete 事件丢失
                    this.recognitionCompleteTimer = setTimeout(() => {
                        if (this.waitingAsrLastMessage) {
                            this.logger.report('OnRecognitionCompleteTimeout');
                            this.processAsrResult();
                            this.waitingAsrLastMessage = false;
                        }
                    }, 3000); // 3秒超时
                }
            } else {
                this.setState('initial');
            }
            if (this.webAudioSpeechRecognizer) {
                await this.webAudioSpeechRecognizer.stop();
                this.webAudioSpeechRecognizer.destroyStream();
                this.webAudioSpeechRecognizer = null;
            }
            this.isStopping = false;
            this.resetRecording();
        },
        /**
         * 录音完成回调
         * @param {Function} callback
         */
        onFinishRecording(callback) {
            this.finishCallbacks.push(callback);
            // 返回一个销毁方法，用于移除回调
            return () => {
                this.finishCallbacks = this.finishCallbacks.filter((cb) => cb !== callback);
            };
        },
        splitTextIntoLines(text) {
            if (!text) return [];
            const lines = [];
            let remainText = text;
            while (remainText.length > 0) {
                if (remainText.length <= 20) {
                    lines.push(remainText);
                    break;
                }
                lines.push(remainText.substring(0, 20));
                remainText = remainText.substring(20);
            }
            return lines;
        },
        cleanup() {
            if (this.recognitionCompleteTimer) {
                clearTimeout(this.recognitionCompleteTimer);
                this.recognitionCompleteTimer = null;
            }
            this.setState('initial');
            this.resetRecording();
            if (this.webAudioSpeechRecognizer) {
                this.webAudioSpeechRecognizer.stop();
                this.webAudioSpeechRecognizer.destroyStream();
                this.webAudioSpeechRecognizer = null;
            }
            this.audioData = markRaw([]);
            this.finishCallbacks = [];
            this.bindOutpatientSheetId = null;
            this.$reset();
        },
    },
});
