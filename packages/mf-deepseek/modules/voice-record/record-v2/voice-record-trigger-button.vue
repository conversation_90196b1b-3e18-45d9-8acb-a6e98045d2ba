<template>
    <div class="voice-record-trigger-button">
        <!-- 触发按钮 -->
        <abc-button
            v-show="showTriggerButton"
            ref="triggerButton"
            :key="buttonRenderKey"
            v-abc-check-electron="{
                customValidate: validateElectron,
                installTitle: '语音病历仅支持客户端，请下载后使用',
            }"
            variant="ghost"
            theme="primary"
            size="small"
            class="voice-record-trigger"
            :class="{ 'is-animating': isButtonAnimating }"
            :icon="attachmentId ? 's-mic-fill' : 's-microphone-line'"
            :disabled="isRecording"
            @click="handleButtonClick"
        >
            语音病历
        </abc-button>

        <voice-record-guide-modal
            v-if="showTipsModal"
            ref="tipsModal"
            v-model="showTipsModal"
            @close="handleTipsModalClose"
        ></voice-record-guide-modal>
    </div>
</template>

<script>
    import { getAsrResult } from '../services/api';
    import { useAsrTipsStore } from '../hooks/use-asr-tips';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { mapGetters } from 'vuex';
    import {
        useVoiceRecordGlobalStore,
    } from './voice-record-global/voice-record-global-manager';
    import { storeToRefs } from 'MfBase/pinia';
    import VoiceRecordGuideModal from './voice-record-guide-modal.vue';

    export default {
        name: 'VoiceRecordTriggerButton',
        components: { VoiceRecordGuideModal },
        props: {
            switchSetting: {
                type: Object,
            },
            outpatientInfo: {
                type: Object,
                required: true,
            },
            patientInfo: {
                type: Object,
                required: true,
            },
            hasAsrResult: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            // 使用 pinia store 获取所有响应式数据和方法
            const store = useAsrTipsStore();

            const {
                isReadTips,
            } = storeToRefs(store);

            const {
                markReadTips,
                initReadTipsStatus,
            } = store;

            const voiceRecordGlobalStore = useVoiceRecordGlobalStore();
            const {
                open: openVoiceRecordGlobal,
            } = voiceRecordGlobalStore;

            const voiceRecordStore = useVoiceRecordStore();
            const {
                state: voiceRecordState,
            } = storeToRefs(voiceRecordStore);

            return {
                isReadTips,
                initReadTipsStatus,
                markReadTips,
                openVoiceRecordGlobal,
                voiceRecordState,
            };
        },
        data() {
            return {
                showTipsModal: false,
                showTriggerButton: false,
                asrResult: null,
                isButtonAnimating: false,
                buttonRenderKey: 0,
            };
        },
        computed: {
            ...mapGetters(['chainBasic']),
            isEnableAiVoiceMrWeb() {
                return this.chainBasic.deepseek?.voiceMrWeb === 1;
            },
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
            attachmentId() {
                return this.asrResult?.attachmentId;
            },
            isRecording() {
                return this.voiceRecordState === 'recording' || this.voiceRecordState === 'paused';
            },
        },
        watch: {
            asrResult: {
                handler() {
                    this.$emit('update:hasAsrResult', !!this.attachmentId);
                },
                deep: true,
            },
        },
        created() {
            this.initReadTipsStatus().then(() => {
                if (this.outpatientInfo?.status === 0) {
                    this.checkReadTips();
                } else {
                    if (this.isReadTips) {
                        this.showTriggerButton = true;
                    }
                }
            });
            this.fetchAsrResult();
        },
        beforeDestroy() {
            if (this.unwatchVoiceRecord) {
                this.unwatchVoiceRecord();
            }
        },
        methods: {
            validateElectron() {
                if (this.isEnableAiVoiceMrWeb) {
                    return '';
                }
                return !window.$abcSocialSecurity?.isElectron ? '语音病历仅支持客户端，请下载后使用' : '';
            },
            async checkReadTips() {
                return new Promise((resolve) => {
                    if (this.isReadTips) {
                        this.showTriggerButton = true;
                        resolve();
                        return;
                    }
                    const image = new Image();
                    // 预加载图片
                    image.src = 'https://static-common-cdn.abcyun.cn/media/voice-record-medical-record-guide.png';
                    image.onload = () => {
                        this.showTipsModal = true;
                    };
                    this.$once('close-tips', () => {
                        this.markReadTips();
                        resolve();
                    });
                });
            },
            async handleTipsModalClose() {
                this.$emit('close-tips');

                // 如果不是第一次显示，直接返回
                if (this.isReadTips) return;

                // FLIP 做动画，modal 缩小到 triggerButton 尺寸，同时按钮渐显

                // 第一步：获取起始位置和尺寸（First）
                const modalEl = this.$refs.tipsModal?.$el.querySelector('.voice-record-guide-dialog');

                if (!modalEl) return;

                const modalRect = modalEl.getBoundingClientRect();

                // 2. 创建Modal克隆节点用于动画
                const modalClone = modalEl.cloneNode(true);
                modalClone.style.position = 'fixed';
                modalClone.style.zIndex = '2001'; // 确保在最上层
                modalClone.style.left = `${modalRect.left}px`;
                modalClone.style.top = `${modalRect.top}px`;
                modalClone.style.width = `${modalRect.width}px`;
                modalClone.style.height = `${modalRect.height}px`;
                modalClone.style.margin = '0';
                modalClone.style.transition = 'none';
                modalClone.style.pointerEvents = 'none';
                modalClone.style.transformOrigin = 'top left';

                // 隐藏关闭按钮
                modalClone.querySelector('.voice-record-guide-close').style.display = 'none';

                const modalWrapper = document.createElement('div');
                modalWrapper.classList.add('abc-modal-dialog');
                modalWrapper.classList.add('abc-dialog-wrapper');
                modalWrapper.appendChild(modalClone);
                document.body.appendChild(modalWrapper);

                // 等待triggerButton渲染完成
                this.showTriggerButton = true;
                // 为了触发一次更新，让 validateElectron 重新计算，否则 disabled 会失效
                this.buttonRenderKey += 1;
                this.isButtonAnimating = true;
                await this.$nextTick();

                // 第二步：获取结束位置和尺寸（Last）
                const buttonEl = this.$refs.triggerButton?.$el;
                const buttonRect = buttonEl.getBoundingClientRect();

                //  创建按钮克隆节点
                const buttonClone = buttonEl.cloneNode(true);
                buttonClone.style.position = 'fixed';
                buttonClone.style.zIndex = '2000';
                buttonClone.style.left = `${buttonRect.left}px`;
                buttonClone.style.top = `${buttonRect.top}px`;
                buttonClone.style.width = `${buttonRect.width}px`;
                buttonClone.style.height = `${buttonRect.height}px`;
                buttonClone.style.transition = 'none';
                buttonClone.style.pointerEvents = 'none';
                buttonClone.style.opacity = '0';
                buttonClone.style.transformOrigin = 'top left';

                document.body.appendChild(buttonClone);

                // 第三步：计算变换（Invert）
                const scaleX = buttonRect.width / modalRect.width;
                const scaleY = buttonRect.height / modalRect.height;
                const translateX = buttonRect.left - modalRect.left;
                const translateY = buttonRect.top - modalRect.top;

                const buttonScaleX = scaleX;
                const buttonScaleY = scaleY;
                const buttonTranslateX = translateX;
                const buttonTranslateY = translateY;

                // 第四步：执行动画（Play）
                // Modal缩小消失动画
                const modalAnimation = modalClone.animate([
                    {
                        transform: 'none',
                        borderRadius: '12px',
                        opacity: 1,
                    },
                    {
                        opacity: 0.9,
                        offset: 0.98,
                    },
                    {
                        transform: `translate(${translateX}px, ${translateY}px) scale(${scaleX}, ${scaleY})`,
                        borderRadius: '6px',
                        opacity: 0,
                    },
                ], {
                    duration: 400,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'both',
                });

                // 按钮淡入放大动画
                const buttonAnimation = buttonClone.animate([
                    {
                        transform: `translate(${-buttonTranslateX}px, ${-buttonTranslateY}px) scale(${1 / buttonScaleX}, ${1 / buttonScaleY})`,
                        opacity: 0,
                    },
                    {
                        opacity: 0,
                        offset: 0.98,
                    },
                    {
                        opacity: 1,
                        transform: 'none',
                    },
                ], {
                    duration: 400,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    fill: 'both',
                });

                buttonAnimation.onfinish = () => {
                    document.body.removeChild(buttonClone);
                    this.isButtonAnimating = false;
                    this.buttonRenderKey += 1;
                };

                // Modal动画结束后移除克隆元素
                modalAnimation.onfinish = () => {
                    document.body.removeChild(modalWrapper);
                };
            },
            async handleButtonClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_entry_btn_clk', '点击语音病历');
                this.unwatchVoiceRecord = this.openVoiceRecordGlobal({
                    outpatientInfo: this.outpatientInfo,
                    patientInfo: this.patientInfo,
                    switchSetting: this.switchSetting,
                    attachmentId: this.attachmentId,
                }, () => {
                    // 重新获取 ASR 结果
                    this.fetchAsrResult();
                });
            },
            async fetchAsrResult() {
                try {
                    const result = await getAsrResult(this.outpatientSheetId);
                    this.asrResult = result;
                } catch (e) {
                    // 忽略错误，继续录音
                    console.warn('获取 ASR 结果失败', e);
                }
            },
        },
    };
</script>

<style lang="scss">
.voice-record-trigger-button {
    .voice-record-trigger {
        &.is-animating {
            pointer-events: none;
            opacity: 0;
        }
    }
}
</style>
