<template>
    <div class="voice-record-global-container">
        <voice-record-dialog
            v-if="dialogVisible"
            v-model="dialogVisible"
            :switch-setting="switchSetting"
            :outpatient-info="outpatientInfo"
            :patient-info="patientInfo"
            :attachment-id="currentAttachmentId"
            @re-record="handleReRecord"
        >
        </voice-record-dialog>
    </div>
</template>

<script>
    export default {
        name: 'VoiceRecordGlobalContainer',
        components: {
            VoiceRecordDialog: () => import('../voice-record-dialog.vue'),
        },
        props: {
            switchSetting: {
                type: Object,
            },
            outpatientInfo: {
                type: Object,
                required: true,
            },
            patientInfo: {
                type: Object,
                required: true,
            },
            attachmentId: {
                type: String,
            },
        },
        data() {
            return {
                dialogVisible: false,
                currentAttachmentId: this.attachmentId,
            };
        },
        computed: {
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
        },
        watch: {
            dialogVisible(value) {
                if (!value) {
                    // 弹窗销毁，触发事件
                    this.$emit('destroy');
                }
            },
        },
        methods: {
            async showRecordDialog() {
                this.dialogVisible = true;
            },
            handleReRecord() {
                this.currentAttachmentId = '';
            },
        },
    };
</script>
