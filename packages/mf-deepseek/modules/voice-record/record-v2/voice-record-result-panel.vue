<template>
    <abc-flex class="voice-record-result-panel-wrapper">
        <!-- 左侧问诊记录 -->
        <div class="conversation-panel">
            <abc-flex vertical style="height: 100%;">
                <abc-flex
                    v-if="isLoadingConversation"
                    align="center"
                    justify="center"
                    style="height: calc(100% - 80px);"
                >
                    <abc-space direction="vertical" size="4">
                        <abc-loading-spinner :loading="isLoadingConversation" size="large" theme="gray"></abc-loading-spinner>
                        <abc-text v-if="!isDetailView" theme="gray">
                            正在保存录音
                        </abc-text>
                    </abc-space>
                </abc-flex>
                <div
                    v-else-if="isSpeakerRecognitionError"
                    style=" width: 100%; height: calc(100% - 80px);"
                >
                    <abc-content-empty></abc-content-empty>
                </div>
                <template v-else>
                    <abc-flex class="panel-header" align="center">
                        <abc-text bold>
                            问诊记录
                        </abc-text>
                    </abc-flex>

                    <abc-list
                        ref="conversationList"
                        class="conversation-list"
                        :data-list="conversationList"
                        :list-item-flex-config="{ align: 'start' }"
                        :custom-padding="[10, 12]"
                        :custom-item-class="(item) => isActiveConversationItem(item) ? 'is-active-conversation' : ''"
                        @click-item="handleConverstaionItemClick"
                    >
                        <template #prepend="{ item }">
                            <!-- 头像 -->
                            <abc-icon
                                icon="s-avatar-fill"
                                size="20"
                                :color="isActiveConversationItem(item) ? 'var(--abc-color-T2)' : 'var(--abc-color-T3)'"
                            ></abc-icon>
                        </template>
                        <template
                            #default="{
                                item, index
                            }"
                        >
                            <div
                                :id="`conversation-item-${ index}`"
                                class="conversation-item-content"
                                :class="{
                                    'doctor-item': item.role === 'doctor',
                                    'patient-item': item.role === 'patient',
                                }"
                            >
                                <div class="speaker-info">
                                    <!-- <abc-text theme="gray" size="normal">
                                        {{ `${item.roleName }` }}
                                    </abc-text> -->
                                    <abc-text :theme="isActiveConversationItem(item) ? 'gray' : 'gray-light'" size="small">
                                        [{{ item.time }}]
                                    </abc-text>
                                </div>
                                <abc-text :theme="isActiveConversationItem(item) ? 'black' : 'gray'" class="conversation-content" size="normal">
                                    {{ item.content }}
                                </abc-text>
                            </div>
                        </template>
                    </abc-list>
                </template>
                <div class="conversation-footer">
                    <div class="voice-record-player">
                        <abc-flex vertical class="player-container">
                            <abc-flex
                                align="center"
                                justify="space-between"
                                class="player-controls"
                                gap="12"
                            >
                                <abc-button
                                    class="play-button"
                                    shape="round"
                                    variant="ghost"
                                    theme="default"
                                    @click="togglePlayback"
                                >
                                    <abc-icon :icon="isPlaying ? 's-pause-fill' : 's-play-fill'" size="16"></abc-icon>
                                </abc-button>
                                <abc-flex gap="middle" align="center" justify="space-between">
                                    <abc-text class="time-display" size="mini" theme="gray">
                                        {{ playbackTimeFormatted }}
                                    </abc-text>
                                    <div
                                        ref="waveContainer"
                                        class="wave-container"
                                        @mousedown="startDrag"
                                        @mousemove="onDrag"
                                        @mouseup="stopDrag"
                                        @mouseleave="stopDrag"
                                    >
                                        <div class="wave-track">
                                            <div class="wave-bars">
                                                <div
                                                    v-for="(bar, index) in waveBars"
                                                    :key="index"
                                                    class="wave-bar"
                                                    :style="{
                                                        height: `${bar}px`,
                                                        background: (progressPercentage / 100) * waveBars.length >= index ? '#1890ff' : '#e6e6e6'
                                                    }"
                                                ></div>
                                            </div>
                                        </div>
                                        <div class="progress-bar-container">
                                            <div class="progress-bar-bg"></div>
                                            <div class="progress-bar" :style="{ width: `${progressPercentage}%` }"></div>
                                        </div>
                                        <div class="progress-handle" :style="{ left: `${progressHandlePosition}%` }"></div>
                                    </div>
                                    <abc-text class="time-display" size="mini" theme="gray">
                                        {{ recordDurationFormated }}
                                    </abc-text>
                                </abc-flex>
                                <abc-button
                                    class="speed-button"
                                    variant="text"
                                    theme="default"
                                    @click="togglePlaybackRate"
                                >
                                    <abc-icon :icon="currentSpeedIcon" size="20"></abc-icon>
                                </abc-button>
                            </abc-flex>
                        </abc-flex>
                    </div>
                </div>
            </abc-flex>
        </div>

        <!-- 右侧病历内容 -->
        <div class="medical-record-panel">
            <abc-flex vertical style="height: 100%;">
                <abc-scrollbar class="medical-record-scroll" padding-size="none">
                    <abc-flex vertical class="medical-record-content">
                        <abc-space class="generate-status">
                            <ai-loading-spinner :loading="analyzeResult.loading"></ai-loading-spinner>
                            <abc-text v-if="analyzeResult.loading" size="normal" theme="gray">
                                生成病历中...
                            </abc-text>
                            <abc-text v-else-if="analyzeResult.error" size="normal" theme="gray">
                                生成失败
                            </abc-text>
                            <abc-text v-else size="normal" theme="gray">
                                已生成病历
                            </abc-text>
                        </abc-space>
                        <!-- 病历头部信息 -->
                        <abc-flex class="medical-record-header" vertical>
                            <abc-text bold style=" font-size: 20px; text-align: center;">
                                门诊病历
                            </abc-text>
                            <abc-flex justify="space-between" style="margin-top: 12px;">
                                <div>
                                    <abc-text theme="gray">
                                        患者：
                                    </abc-text>
                                    <abc-text theme="gray">
                                        {{ patientInfo?.name || '匿名患者' }}{{ patientInfo?.sex ? `&nbsp;&nbsp;${patientInfo?.sex}` : '' }}{{ patientInfo?.age?.year ? `&nbsp;&nbsp;${patientInfo?.age?.year }岁` : '' }}
                                    </abc-text>
                                </div>
                                <abc-text theme="gray">
                                    就诊时间：{{ diagnosisTime }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>

                        <abc-divider variant="dashed"></abc-divider>

                        <!-- 病历内容 -->
                        <markdown-renderer v-if="analyzeResult.content" :content="analyzeResult.content"></markdown-renderer>
                        <!-- 生成错误 -->
                        <ai-error-tips v-if="analyzeResult.error">
                            {{ analyzeResult.error }}
                            <template v-if="analyzeResult.canRetry">
                                ，<abc-link size="small" theme="primary" @click="handleRetryClick">
                                    点击重试
                                </abc-link>
                            </template>
                        </ai-error-tips>
                    </abc-flex>
                </abc-scrollbar>

                <!-- 操作按钮 -->
                <abc-flex class="action-buttons" justify="end">
                    <abc-button
                        shape="round"
                        variant="ghost"
                        size="large"
                        theme="danger"
                        width="96"
                        @click="handleReRecordClick"
                    >
                        重新录制
                    </abc-button>
                    <abc-button
                        v-if="analyzeResult.content && !analyzeResult.error && !analyzeResult.loading"
                        shape="round"
                        size="large"
                        width="96"
                        class="adopt-button"
                        @click="handleAcceptClick"
                    >
                        采纳
                    </abc-button>
                </abc-flex>
            </abc-flex>
        </div>
    </abc-flex>
</template>

<script>
    import AudioPlayer from '@/common/components/audio-player';
    import { formatDate } from '@abc/utils-date';
    import AiLoadingSpinner from '@/common/components/ai-loading-spinner.vue';
    import { useVoiceAnalyzeStore } from '../hooks/use-voice-analyze';
    import { storeToRefs } from 'MfBase/pinia';
    import {
        formatTime,
        sleep,
        smoothScroll,
    } from '@/common/utils';
    import {
        createWavFile,
    } from '@/common/utils/audio';
    import MarkdownRenderer from '../record-dialog/markdown-renderer.vue';
    import AiErrorTips from '@/common/components/ai-error-tips.vue';
    import { EVENT_MR_BUS_NAME } from '@/common/utils/constant';
    import {
        saveAsrResult,
        getAsrResultDetail,
        querySpeakerRecognitionStatus,
        submitSpeakerRecognitionTask,
    } from '../services/api';
    import config from '../config.js';

    export default {
        name: 'VoiceRecordResultPanel',
        components: {
            AiLoadingSpinner,
            MarkdownRenderer,
            AiErrorTips,
        },
        props: {
            /**
             * 病历字段开关设置
             */
            switchSetting: {
                type: Object,
            },
            /**
             * 患者信息
             */
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            /**
             * 门诊信息
             */
            outpatientInfo: {
                type: Object,
                default: () => ({}),
            },
            /**
             * 附件 ID
             */
            attachmentId: {
                type: String,
                default: '',
            },
            /**
             * 录音数据
             * @type {{audioData: Array, asrResult: Array}}
             */
            recordData: {
                type: Object,
                default: () => null,
            },
        },
        setup() {
            const voiceAnalyzeStore = useVoiceAnalyzeStore();
            const {
                analyzeResult,
            } = storeToRefs(voiceAnalyzeStore);
            const {
                setMedicalRecord,
                startVoiceAnalyze,
                destroy,
            } = voiceAnalyzeStore;
            return {
                analyzeResult,
                setMedicalRecord,
                startVoiceAnalyze,
                destroyVoiceAnalyze: destroy,
            };
        },
        data() {
            return {
                uploadAudioUrl: '',
                generateError: '',
                isGenerating: false,
                generationError: false,
                isLoadingConversation: false,
                // 播放器相关数据
                audioPlayer: null,
                isPlaying: false,
                currentTime: 0, // 播放器当前时间，单位为毫秒
                waveBars: Array(110).fill(0).map(() => Math.floor(Math.random() * 22)),
                currentBarIndex: 0,
                playbackInterval: null,
                playbackRate: 1.0,
                isDragging: false,
                recordDuration: 0,
                scrollOffset: 0, // 滚动偏移

                asrResultDetail: null,
                isDestroy: false,
            };
        },
        computed: {
            /**
             * 是否是查看详情模式
             */
            isDetailView() {
                return !!this.attachmentId;
            },
            currentActiveIndex() {
                // 找到当前播放项的 index
                return this.conversationList.findIndex((item) => this.isActiveConversationItem(item));
            },
            progressPercentage() {
                return this.recordDuration > 0 ? (this.currentTime / this.recordDuration) * 100 : 0;
            },
            progressHandlePosition() {
                return this.recordDuration > 0 ? (this.currentTime / this.recordDuration) * 93.8 : 0;
            },
            playbackTimeFormatted() {
                return this.formatTime(this.currentTime);
            },
            recordDurationFormated() {
                return this.formatTime(this.recordDuration);
            },
            outpatientSheetId() {
                return this.outpatientInfo?.id;
            },
            diagnosisTime() {
                return formatDate(this.outpatientInfo?.created, 'YYYY-MM-DD HH:mm:ss');
            },
            asrContent() {
                if (this.recordData?.asrResult?.length > 0) {
                    return this.recordData.asrResult.map((item) => `[${item.time}]: ${item.text}`).filter(Boolean).join('\n');
                }
                return '';
            },
            isSpeakerRecognitionError() {
                return this.asrResultDetail?.asrMetadataList?.length === 0;
            },
            conversationList() {
                const roleChNum = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
                const metadataList = this.asrResultDetail?.asrMetadataList || [];

                // 先获取原始数据
                const rawList = metadataList.map((item) => {
                    const speaker = item.additions?.speaker;
                    const role = speaker === '1' ? 'doctor' : 'patient';
                    const roleName = speaker && roleChNum[speaker - 1] ? `说话人${roleChNum[speaker - 1]}` : '匿名';
                    return {
                        role,
                        roleName,
                        content: item.text,
                        time: formatTime(item.startTime),
                        startTime: +item.startTime,
                        endTime: +item.endTime,
                        // 添加原始结束时间，用于后续处理
                        originalEndTime: +item.endTime,
                    };
                });

                // 处理时间间隔，确保前一个元素的结束时间等于下一个元素的开始时间
                return rawList.map((item, index) => {
                    // 如果不是最后一个元素，则结束时间等于下一个元素的开始时间
                    if (index < rawList.length - 1) {
                        item.endTime = rawList[index + 1].startTime - 1;
                    } else if (this.recordDuration && item.endTime < this.recordDuration) {
                        // 如果是最后一个元素且结束时间小于录音总时长，则延伸到录音结束
                        item.endTime = this.recordDuration;
                    }
                    return item;
                });
                // return this.asrResultDetail?.speakerRecognitionResult?.result?.utterances?.map((item) => {
                //     const speaker = item.additions?.speaker;
                //     const role = speaker === '1' ? 'doctor' : 'patient';
                //     const roleName = speaker && roleChNum[speaker - 1] ? `说话人${roleChNum[speaker - 1]}` : '匿名';
                //     return {
                //         role,
                //         roleName,
                //         content: item.text,
                //         time: formatTime(item.startTime),
                //         startTime: item.startTime,
                //         endTime: item.endTime,
                //     };
                // }) || [];
            },
            currentSpeedIcon() {
                switch (this.playbackRate) {
                    case 1.0:
                        return 's-a-1x-fill';
                    case 1.5:
                        return 's-a-15x-fill';
                    case 2.0:
                        return 's-a-2x-fill';
                    default:
                        return 's-a-1x-fill';
                }
            },
        },
        watch: {
            currentActiveIndex(newIdx) {
                this.scrollToActiveConversationItem(newIdx);
            },
        },
        async created() {
            this.isLoadingConversation = true;
            // 传了 attachmentId，获取详情
            if (this.isDetailView) {
                try {
                    // 获取详情数据
                    const asrResultDetail = await getAsrResultDetail(this.outpatientSheetId, this.attachmentId);
                    console.log('asrResultDetail', asrResultDetail);

                    if (asrResultDetail) {
                        // 设置录音时长
                        if (asrResultDetail.duration) {
                            this.recordDuration = asrResultDetail.duration;
                        }

                        this.asrResultDetail = asrResultDetail;

                        // 设置病历内容
                        this.setMedicalRecord(asrResultDetail.medicalRecord);

                        // if (!asrResultDetail.speakerRecognitionTaskId) {
                        //     //  没有说话人分离信息，可能是之前异常没有提交，重新提交任务
                        //     await this.submitSpeakerRecognitionTask(asrResultDetail.attachmentId, {
                        //         format: 'wav',
                        //         url: this.asrResultDetail.voiceUrl,
                        //     });
                        // }
                        //
                        // if (asrResultDetail.speakerRecognitionTaskStatus !== 20 && asrResultDetail.speakerRecognitionTaskStatus !== 99) {
                        //     //  开始轮询说话人分离任务状态
                        //     await this.pollSpeakerRecognitionStatus();
                        // }
                    } else {
                        console.error('获取ASR详情失败');
                        this.$Toast({
                            type: 'error',
                            message: '获取录音详情失败',
                        });
                    }
                } catch (error) {
                    console.error('获取ASR详情异常:', error);
                    this.$Toast({
                        type: 'error',
                        message: '获取录音详情异常',
                    });
                } finally {
                    this.isLoadingConversation = false;
                }
            }

            // 初始化音频播放器
            this.initAudioPlayer();

            // 如果不是详情模式，需要生成病历、上传音频、保存ASR结果、提交说话人分离任务
            if (!this.isDetailView) {
                if (config.debug) {
                    await sleep(3000).promise;
                    this.isLoadingConversation = false;
                    return;
                }
                this.logger = this.recordData.logger;
                try {
                    this.asrResultDetail = {
                        asrMetadataList: this.recordData.asrResult,
                        speakerRecognitionTaskId: null,
                        speakerRecognitionTaskStatus: 0,
                        speakerRecognitionResult: null,
                    };
                    this.isLoadingConversation = false;
                    // 并行执行生成病历和上传音频
                    await Promise.all([
                        this.generateMedicalRecord(this.asrContent, this.switchSetting),
                        this.uploadAudioToOSS(this.recordData.audioData),
                    ]);

                    this.logger.report('生成病历结果', {
                        duration: this.recordDuration,
                        uploadAudioUrl: this.uploadAudioUrl,
                        analyzeResult: this.analyzeResult,
                    });

                    // 存储到后台便于后续分析
                    if (this.outpatientSheetId && this.uploadAudioUrl) {
                        const saveAsrResultRsp = await saveAsrResult(this.outpatientSheetId, {
                            uploadAudioUrl: this.uploadAudioUrl,
                            asrResult: this.recordData.asrResult,
                            medicalRecord: this.analyzeResult.content || '',
                            duration: this.recordDuration,
                        });

                        if (!saveAsrResultRsp?.data?.id) {
                            this.$Toast({
                                type: 'error',
                                message: '存储 ASR 结果失败',
                            });
                            return;
                        }

                        if (this.analyzeResult?.canRetry) {
                            this.retryPayload = {
                                attachmentId: saveAsrResultRsp.data.id,
                                outpatientSheetId: this.outpatientSheetId,
                                uploadAudioUrl: this.uploadAudioUrl,
                                asrResult: this.recordData.asrResult,
                                medicalRecord: '',
                                duration: this.recordDuration,
                            };
                        }

                        this.asrResultDetail = Object.assign(this.asrResultDetail, {
                            attachmentId: saveAsrResultRsp.data.id,
                            voiceUrl: this.uploadAudioUrl,
                            duration: this.recordDuration,
                        });

                        // 提交说话人分离任务
                        // await this.submitSpeakerRecognitionTask(this.asrResultDetail.attachmentId, {
                        //     format: 'wav',
                        //     url: this.uploadAudioUrl,
                        // });

                        // 轮询说话人分离任务状态
                        // await this.pollSpeakerRecognitionStatus();
                    }
                } catch (error) {
                    this.logger.report('生成病历异常', {
                        error,
                    });
                    console.error('处理录音数据失败:', error);
                    this.$Toast({
                        type: 'error',
                        message: '处理录音数据失败',
                    });
                }
            }
        },
        beforeDestroy() {
            this.isDestroy = true;
            this.logger = null;
            if (this.audioPlayer) {
                this.stopPlayback();
                this.audioPlayer.destroy();
                this.audioPlayer = null;
            }
            this.destroyVoiceAnalyze();
        },
        methods: {
            formatTime,
            formatDate,
            initAudioPlayer() {
                let audioUrl = '';
                if (this.asrResultDetail?.voiceUrl) {
                    audioUrl = this.asrResultDetail.voiceUrl;
                } else if (this.recordData?.audioData && this.recordData.audioData.length > 0) {
                    // 用组件自己的 createWavFile 方法生成标准 wav
                    const wavBlob = createWavFile(this.recordData.audioData);
                    audioUrl = URL.createObjectURL(wavBlob);
                }
                this.audioPlayer = new AudioPlayer();
                this.audioPlayer.setSrc(audioUrl);
                this.audioPlayer.setPlaybackRate(this.playbackRate);

                this.audioPlayer.on('loadedmetadata', () => {
                    // 如果没有从后端获取到时长，则使用音频文件的时长
                    if (!this.recordDuration) {
                        this.recordDuration = this.audioPlayer.getDuration() * 1000;
                    }
                    console.log('loadedmetadata ', this.recordDuration, this.audioPlayer.getDuration());
                });
                this.audioPlayer.on('play', () => {
                    const updateTime = () => {
                        if (!this.isPlaying) {
                            return;
                        }
                        const currentTime = this.audioPlayer.getCurrentTime() * 1000;
                        this.currentTime = currentTime;
                        requestAnimationFrame(updateTime);
                    };
                    updateTime();
                });
                this.audioPlayer.on('ended', () => {
                    this.isPlaying = false;
                    this.currentTime = this.recordDuration;
                });
                this.generateWaveformData();
            },
            // 生成真实的波形数据
            async generateWaveformData() {
                const barCount = 110;
                const barMaxHeight = 22;
                const barMinHeight = 0;

                // 生成随机波形的辅助函数
                const generateRandomBars = () => {
                    const bars = [];
                    for (let i = 0; i < barCount; i++) {
                        const height = Math.floor(Math.random() * barMaxHeight) + barMinHeight;
                        bars.push(height);
                    }
                    return bars;
                };

                // 检查是否有音频数据
                const hasAudioData = this.recordData?.audioData && this.recordData.audioData.length > 0;
                const hasVoiceUrl = !!this.asrResultDetail?.voiceUrl;

                if (!hasAudioData && !hasVoiceUrl) {
                    // 没有任何音频数据时生成随机波形
                    this.waveBars = generateRandomBars();
                    return;
                }

                try {
                    let audioBuffer;
                    const sampleRate = 16000;
                    const numChannels = 1;

                    if (hasAudioData) {
                        // 使用 PCM 数据生成 AudioBuffer
                        // 注意：这里不再重复创建 wavBlob，而是使用 initAudioPlayer 中已创建的 URL
                        const wavBlob = createWavFile(this.recordData.audioData);
                        const arrayBuffer = await wavBlob.arrayBuffer();
                        const audioContext = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(numChannels, sampleRate * 10, sampleRate);
                        audioBuffer = await new Promise((resolve, reject) => {
                            audioContext.decodeAudioData(arrayBuffer, resolve, reject);
                        });
                    } else if (hasVoiceUrl) {
                        // 使用 voiceUrl 获取音频数据
                        try {
                            const response = await fetch(this.asrResultDetail.voiceUrl);
                            const arrayBuffer = await response.arrayBuffer();
                            const audioContext = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(numChannels, sampleRate * 10, sampleRate);
                            audioBuffer = await new Promise((resolve, reject) => {
                                audioContext.decodeAudioData(arrayBuffer, resolve, reject);
                            });
                        } catch (error) {
                            console.error('从 URL 获取音频数据失败:', error);
                            this.waveBars = generateRandomBars();
                            return;
                        }
                    }

                    // 创建离线音频上下文来分析音频数据
                    const offlineContext = new OfflineAudioContext(
                        audioBuffer.numberOfChannels,
                        audioBuffer.length,
                        audioBuffer.sampleRate,
                    );

                    // 创建音频源
                    const source = offlineContext.createBufferSource();
                    source.buffer = audioBuffer;

                    // 创建分析器
                    const analyzer = offlineContext.createAnalyser();
                    analyzer.fftSize = 256;

                    // 连接音频节点
                    source.connect(analyzer);
                    analyzer.connect(offlineContext.destination);

                    // 开始播放
                    source.start(0);

                    // 处理音频数据
                    try {
                        const renderedBuffer = await offlineContext.startRendering();
                        // 获取频域数据
                        const numBars = barCount; // 显示的频谱条数
                        const bars = [];

                        // 从音频缓冲区中提取数据
                        const channelData = renderedBuffer.getChannelData(0);
                        const blockSize = Math.floor(channelData.length / numBars);

                        for (let i = 0; i < numBars; i++) {
                            let sum = 0;
                            for (let j = 0; j < blockSize; j++) {
                                const sampleIndex = (i * blockSize) + j;
                                if (sampleIndex < channelData.length) {
                                    sum += Math.abs(channelData[sampleIndex]);
                                }
                            }
                            // 计算平均振幅并映射到合适的高度范围（0-22像素）
                            const average = sum / blockSize;
                            const height = Math.max(barMinHeight, Math.min(barMaxHeight, Math.floor(average * 150)));
                            bars.push(height);
                        }

                        this.waveBars = bars;
                    } catch (error) {
                        console.error('音频分析失败:', error);
                        // 分析失败时使用随机数据
                        this.waveBars = generateRandomBars();
                    }
                } catch (error) {
                    console.error('创建音频分析器失败:', error);
                    // 出错时使用随机数据
                    this.waveBars = generateRandomBars();
                }
            },
            updatePaddingSize() {
                const wrapper = this.$el.querySelector('.conversation-list');
                const lastEl = this.$el.querySelector(`#conversation-item-${this.conversationList.length - 1}`);
                if (wrapper && lastEl) {
                    const lastRect = lastEl.getBoundingClientRect();
                    const wrapperRect = wrapper.getBoundingClientRect();
                    const padding = wrapperRect.height - 150 - lastRect.height;
                    wrapper.style.paddingBottom = `${padding}px`;
                }
            },
            scrollToActiveConversationItem(index) {
                // 让当前项距离容器顶部 150px，使用自定义动画在300ms内完成滚动
                this.$nextTick(() => {
                    const wrapper = this.$el.querySelector('.conversation-list');
                    const activeEl = this.$el.querySelector(`#conversation-item-${index}`);

                    if (wrapper && activeEl) {
                        const wrapperRect = wrapper.getBoundingClientRect();
                        const activeRect = activeEl.getBoundingClientRect();
                        // 当前 active 距离 wrapper 顶部的距离
                        const offset = activeRect.top - wrapperRect.top;
                        // 目标是让 active 距离 wrapper 、顶部 150px
                        // 计算目标位置并限制边界
                        const rawTarget = wrapper.scrollTop + offset - 150;
                        const targetScroll = Math.max(
                            0,
                            Math.min(
                                wrapper.scrollHeight - wrapper.clientHeight,
                                rawTarget,
                            ),
                        );

                        // 调用通用滚动函数
                        smoothScroll(wrapper, targetScroll, 300, 'easeInOutQuad');
                    }
                });
            },
            handleConverstaionItemClick(e, item) {
                // 点击某项，跳转到该项对应的时间
                if (item && this.audioPlayer) {
                    this.currentTime = item.startTime;
                    this.audioPlayer.setCurrentTime(item.startTime / 1000);
                }
            },
            isActiveConversationItem(item) {
                return item.startTime <= this.currentTime && item.endTime > this.currentTime;
            },
            togglePlayback() {
                if (!this.audioPlayer) return;
                if (this.isPlaying) {
                    this.pauseAudio();
                } else {
                    this.playAudio();
                }
            },
            playAudio() {
                if (!this.audioPlayer) return;
                this.audioPlayer.play();
                this.isPlaying = true;
            },
            pauseAudio() {
                if (!this.audioPlayer) return;
                this.audioPlayer.pause();
                this.isPlaying = false;
            },
            setPlaybackRate(rate) {
                this.playbackRate = rate;
                if (this.audioPlayer) {
                    this.audioPlayer.setPlaybackRate(rate);
                }
            },
            stopPlayback() {
                this.isPlaying = false;
                this.currentTime = 0;
                this.currentBarIndex = 0;

                this.timeUpdateCallback = null;
                this.endedCallback = null;
            },

            togglePlaybackRate() {
                // 1x 1.5x 2.0x 循环
                const rates = [1.0, 1.5, 2.0];
                const index = rates.indexOf(this.playbackRate);
                this.setPlaybackRate(rates[(index + 1) % rates.length]);
            },

            // 拖动进度相关方法
            startDrag(event) {
                if (!this.audioPlayer) return;
                this.isDragging = true;
                // 拖动开始时，若正在播放则暂停，并记录需要恢复
                this._shouldResumeAfterDrag = false;
                if (this.isPlaying) {
                    this.pauseAudio();
                    this._shouldResumeAfterDrag = true;
                }
                this.updateProgressFromEvent(event);
            },

            onDrag(event) {
                if (!this.isDragging || !this.audioPlayer) return;
                this.updateProgressFromEvent(event);
            },
            stopDrag() {
                if (!this.audioPlayer) return;
                this.isDragging = false;
                if (this.recordDuration > 0) {
                    const newTime = (this.progressPercentage / 100) * (this.recordDuration / 1000);
                    this.audioPlayer.setCurrentTime(newTime);
                }
                // 拖动结束后，若之前在播放则恢复播放，否则停在当前位置
                if (this._shouldResumeAfterDrag) {
                    this.playAudio();
                }
                this._shouldResumeAfterDrag = false;
            },
            updateProgressFromEvent(event) {
                if (!this.$refs.waveContainer) return;

                const { waveContainer } = this.$refs;
                const rect = waveContainer.getBoundingClientRect();
                const offsetX = event.clientX - rect.left;
                const containerWidth = rect.width;

                // 计算百分比，并限制在0-100之间
                let percentage = (offsetX / containerWidth) * 100;
                percentage = Math.max(0, Math.min(100, percentage));

                // 更新当前时间显示
                if (this.recordDuration > 0) {
                    this.currentTime = (percentage / 100) * this.recordDuration;
                }
            },

            async uploadAudioToOSS(audioData) {
                if (!this.outpatientSheetId) {
                    return;
                }
                if (audioData.length === 0) {
                    return;
                }

                if (!this.asrContent) {
                    return;
                }
                try {
                    // 将Int8Array数据转换为WAV格式
                    const wavBlob = createWavFile(audioData);

                    // 创建File对象
                    const fileName = `voice_record_${new Date().getTime()}.wav`;
                    const audioFile = new File([wavBlob], fileName, { type: 'audio/wav' });

                    // 上传到OSS
                    const options = {
                        filePath: 'voice-records', // 业务路径
                    };

                    // 使用临时目录上传，避免占用空间
                    const result = await this.$abcPlatform.service.oss.uploadForTemp(options, audioFile, (percentage) => {
                        console.debug('上传进度：', percentage);
                    });

                    console.debug('录音文件上传成功：', result.url);
                    this.uploadAudioUrl = result.url;
                } catch (error) {
                    console.error('录音文件上传失败：', error);
                    this.logger.report('录音上传失败', {
                        err: error,
                    });
                }
            },

            async submitSpeakerRecognitionTask(attachmentId, audio) {
                if (!attachmentId || !audio) {
                    console.error('提交说话人分离任务缺少必要参数');
                    return null;
                }

                try {
                    // 提交说话人分离任务
                    const result = await submitSpeakerRecognitionTask(attachmentId, audio);
                    if (!result) {
                        this.$Toast({
                            type: 'error',
                            message: '角色分离任务提交失败',
                        });
                        return null;
                    }

                    return result;
                } catch (error) {
                    console.error('提交说话人分离任务异常:', error);
                    this.$Toast({
                        type: 'error',
                        message: '角色分离任务提交异常',
                    });
                    return null;
                }
            },
            async pollSpeakerRecognitionStatus() {
                if (this.isDestroy) {
                    return;
                }
                // 使用当前的说话人分离任务ID
                const taskId = this.asrResultDetail?.attachmentId;
                if (!taskId) {
                    console.error('没有有效的说话人分离任务ID');
                    return;
                }

                try {
                    const speakerRecognitionInfo = await querySpeakerRecognitionStatus(taskId);

                    // 如果没有结果，等待一秒后继续轮询
                    if (speakerRecognitionInfo?.status === 20 || speakerRecognitionInfo?.status === 99) {
                        const {
                            status,
                            taskResult,
                        } = speakerRecognitionInfo;

                        // 更新说话人分离信息
                        this.asrResultDetail.speakerRecognitionResult = taskResult;
                        this.asrResultDetail.speakerRecognitionTaskStatus = status;
                        return;
                    }

                    await sleep(1000).promise;
                    await this.pollSpeakerRecognitionStatus();
                } catch (error) {
                    console.error('轮询说话人分离任务失败:', error);
                    // 出错后等待一段时间再重试
                    await sleep(2000).promise;
                    return this.pollSpeakerRecognitionStatus();
                }
            },
            // 生成病历
            async generateMedicalRecord(asrContent, switchSetting) {
                this.isGenerating = true;
                try {
                    const context = {
                        businessId: this.outpatientSheetId,
                        patientId: this.patientInfo?.id,
                        patientOrderId: this.outpatientInfo?.patientOrderId,
                    };
                    await this.startVoiceAnalyze(asrContent, switchSetting, context);
                } catch (error) {
                    console.error('生成病历错误:', error);
                    this.logger.report('生成病历错误', {
                        err: error,
                    });
                } finally {
                    this.isGenerating = false;
                }
            },
            // 解析病历内容，根据 AllMRKey 中定义的字段提取对应内容
            parseMedicalRecord(medicalRecord, mrKeys) {
                const result = {};
                if (!medicalRecord) {
                    return result;
                }

                const trimedMedicalRecord = medicalRecord.replaceAll('**', '');

                // 遍历所有病历字段
                mrKeys.forEach((item) => {
                    const {
                        key, matchLabel,
                    } = item;
                    const regex = new RegExp(`${matchLabel}[：:](.*?)(?=\\n\\n|\\n[^\\n]|$)`, 's');
                    const match = trimedMedicalRecord?.match(regex);

                    if (match && match[1]?.trim()) {
                        result[key] = match[1].trim();
                    }
                });

                return result;
            },

            handleAcceptClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_accept', '采纳语音病历');
                const AllMRKey = [
                    {
                        key: 'chiefComplaint', label: '主诉', matchLabel: '主诉',
                    },
                    {
                        key: 'presentHistory', label: '现病史', matchLabel: '现病史',
                    },
                    {
                        key: 'pastHistory', label: '既往史', matchLabel: '既往史',
                    },
                    {
                        key: 'familyHistory', label: '家族史', matchLabel: '家族史',
                    },
                    {
                        key: 'allergicHistory', label: '过敏史', matchLabel: '过敏史',
                    },
                    {
                        key: 'personalHistory', label: '个人史', matchLabel: '个人史',
                    },
                    {
                        key: 'obstetricalHistory', label: '月经婚育史', matchLabel: '月经婚育史',
                    },
                    {
                        key: 'physicalExamination', label: '体格检查', matchLabel: '体格检查',
                    },
                    {
                        key: 'chineseExamination', label: '望闻切诊', matchLabel: '望闻切诊',
                    },
                    {
                        key: 'oralExamination', label: '口腔检查', matchLabel: '口腔检查',
                    },
                    {
                        key: 'auxiliaryExaminations', label: '辅助检查', matchLabel: '辅助检查',
                    },
                    {
                        key: 'diagnosis', label: '诊断', matchLabel: '诊断',
                    },
                    {
                        key: 'syndrome', label: '辨证', matchLabel: '辨证',
                    },
                    {
                        key: 'therapy', label: '治法', matchLabel: '治法',
                    },
                    {
                        key: 'disposals', label: '处置', matchLabel: '处置-口腔',
                    },
                ];

                try {
                    // 解析病历内容
                    const medicalRecordData = this.parseMedicalRecord(this.analyzeResult.content, AllMRKey);
                    Object.assign(medicalRecordData, {
                        outpatientSheetId: this.outpatientSheetId,
                    });
                    console.log('解析后的病历数据:', { ...medicalRecordData });

                    // 发送解析后的病历数据
                    this.$abcEventBus.$emit(EVENT_MR_BUS_NAME, {
                        type: 'medicalRecord',
                        value: medicalRecordData,
                    });
                    // 关闭对话框
                    this.$emit('finish');
                } catch (error) {
                    console.error('采纳病历失败:', error);
                }
            },

            async handleRetryClick() {
                await this.generateMedicalRecord(this.asrContent, this.switchSetting);

                if (this.retryPayload) {
                    const {
                        attachmentId,
                        outpatientSheetId,
                        ...rest
                    } = this.retryPayload;
                    rest.medicalRecord = this.analyzeResult.content || '';
                    await saveAsrResult(outpatientSheetId, rest, attachmentId);
                }
            },
            handleReRecordClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_re_record_btn_clk', '点击重新录音');
                this.$emit('re-record');
            },
        },
    };
</script>

<style lang="scss">
.voice-record-result-panel-wrapper {
    display: flex;
    width: 100%;
    height: 100%;

    // 左侧问诊记录面板
    .conversation-panel {
        flex: 1;
        max-width: 440px;
        height: 100%;
        background-color: #f9fafc;
        border-right: 1px solid #eaedf1;

        .panel-header {
            height: 56px;
            padding: 0 24px;
        }

        .conversation-list {
            height: calc(100% - 56px - 80px) !important;
            padding-right: 2px;
            padding-bottom: 150px;
            padding-left: 12px;
            transform: translateZ(0);
            will-change: scroll-position;

            .is-active-conversation {
                background-color: var(--abc-color-cp-grey4);
            }

            .conversation-item-content {
                flex: 1;

                .speaker-info {
                    display: inline-flex;
                    gap: 8px;
                    justify-content: space-between;
                }

                .conversation-content {
                    display: block;
                    word-break: break-all;
                }
            }
        }

        .conversation-footer {
            position: relative;
            display: flex;
            align-items: center;
            height: 80px;
            margin: 0 24px;
            border-top: 1px solid #eaedf1;

            &::before {
                position: absolute;
                bottom: calc(100% + 1px);
                left: -12px;
                width: 415px;
                height: 40px;
                pointer-events: none;
                // 做个遮罩
                content: '';
                background: linear-gradient(180deg, rgba(249, 250, 252, 0) 0%, rgba(249, 250, 252, 0.8) 100%);
            }

            .voice-record-player {
                width: 100%;

                .player-container {
                    width: 100%;
                }

                .player-controls {
                    width: 100%;
                    height: 32px;
                }

                .wave-container {
                    position: relative;
                    display: flex;
                    align-items: center;
                    width: 226px;
                    height: 32px;

                    .wave-track {
                        position: absolute;
                        bottom: 3px;
                        display: flex;
                        align-items: center;
                        width: 100%;
                        padding: 0;
                        transform: translateY(-50%);

                        .wave-bars {
                            position: relative;
                            bottom: 4px;
                            left: 3px;
                            z-index: 0;
                            display: flex;
                            gap: 1px;
                            align-items: flex-end;
                            justify-content: center;
                            width: 220px;
                            height: 22px;

                            .wave-bar {
                                flex: 1;
                                width: 1px;
                                min-width: 1px;
                                max-width: 1px;
                                background-color: var(--abc-color-P10, #e0e2eb);
                                border-radius: 50px;
                                transition: background-color 0.2s;
                            }
                        }
                    }

                    .progress-bar-container {
                        position: absolute;
                        z-index: 1;
                        display: flex;
                        align-items: center;
                        width: 100%;
                        height: 4px;
                        overflow: hidden;
                        cursor: pointer;
                        border-radius: 2px;
                    }

                    .progress-bar-bg {
                        position: absolute;
                        width: 100%;
                        height: 4px;
                        background: var(--abc-color-P1, #e0e2eb);
                        border-radius: 2px;
                    }

                    .progress-bar {
                        position: absolute;
                        height: 4px;
                        background: var(--abc-color-B2);
                        border-radius: 2px;
                    }

                    .progress-handle {
                        position: absolute;
                        z-index: 3;
                        width: 14px;
                        height: 14px;
                        cursor: pointer;
                        background: var(--abc-color-B2);
                        border: 2px solid #ffffff;
                        border-radius: 50%;
                        box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);

                        &:hover {
                            background: var(--abc-color-B3);
                        }

                        &:active {
                            background: var(--abc-color-theme1);
                        }
                    }
                }
            }
        }
    }

    // 右侧病历内容面板
    .medical-record-panel {
        flex: 1;
        min-width: 240px;
        height: 100%;
        background-color: #ffffff;

        .panel-header {
            height: 56px;
            padding: 0 16px;
            border-bottom: 1px solid #eaedf1;
        }

        .medical-record-scroll {
            height: calc(100% - 80px); // 减去头部和底部按钮区域高度
            padding: 16px 90px 16px 100px;

            .generate-status {
                width: fit-content;
                padding: 6px 12px;
                background: var(--abc-color-cp-grey4);
                border-radius: var(--abc-border-radius-huge, 50px);
            }

            .medical-record-header {
                margin-top: 16px;
            }
        }

        .generating-status {
            height: 200px;
        }

        .error-status {
            height: 200px;
        }

        .medical-record-sections {
            gap: 16px;
        }

        .medical-record-section {
            margin-bottom: 16px;

            .abc-text {
                line-height: 1.6;
            }
        }

        .action-buttons {
            align-items: center;
            height: 80px;
            margin: 0 24px;
            border-top: 1px solid #eaedf1;

            .adopt-button {
                min-width: 96px;
                background: linear-gradient(135deg, #5acafd 0%, #5694fe 51.15%, #9055fe 100%);
                border: none;

                &:hover {
                    opacity: 0.9;
                }
            }
        }
    }
}
</style>
