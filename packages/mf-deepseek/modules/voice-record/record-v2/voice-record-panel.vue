<template>
    <div class="voice-record-panel">
        <div class="voice-record-main">
            <abc-text size="large" bold class="voice-record-title">
                语音病历
            </abc-text>
            <!-- 中间内容 -->
            <template v-if="state === 'initial'">
                <abc-flex
                    vertical
                    align="center"
                    gap="middle"
                >
                    <abc-icon icon="s-track-line" size="28px" color="var(--abc-color-T2)"></abc-icon>
                    <abc-text theme="gray" size="large" class="voice-record-subtitle">
                        医生专注沟通，AI精准记录
                    </abc-text>
                </abc-flex>
            </template>
            <template v-else>
                <div class="audio-wave">
                    <abc-text class="record-time" size="normal" theme="gray">
                        {{ formattedTime }}
                    </abc-text>
                    <wave-visualization
                        class="wave-visualization"
                        :is-paused="state === 'paused'"
                        :waveform-data="waveformData"
                        :width="468"
                    ></wave-visualization>
                    <div class="subtitle-container">
                        <div class="subtitle-wrapper">
                            <div class="subtitle-content">
                                {{ fullSentence }}
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <!--  底部操作  -->
            <abc-flex
                vertical
                gap="large"
                align="center"
            >
                <div style="height: 48px;"></div>
                <!-- 初始按钮动画 -->
                <transition name="record-btn-fade">
                    <abc-button
                        v-show="state === 'initial'"
                        class="start-button"
                        shape="round"
                        theme="success"
                        variant="fill"
                        size="large"
                        width="196"
                        icon="s-mic-fill"
                        :loading="isStarting"
                        @click="handleStartBtnClick"
                    >
                        开始
                    </abc-button>
                </transition>
                <!-- 暂停/继续/结束按钮动画 -->
                <transition
                    name="record-btn-enter"
                >
                    <div v-show="state !== 'initial'" class="record-controls">
                        <abc-button
                            v-show="state === 'recording'"
                            key="pause"
                            class="pause-btn"
                            shape="round"
                            theme="warning"
                            variant="ghost"
                            size="large"
                            width="116"
                            icon="s-pause-fill"
                            @click="pauseRecording"
                        >
                            暂停
                        </abc-button>
                        <abc-button
                            v-show="state === 'paused'"
                            key="continue"
                            class="continue-btn"
                            shape="round"
                            theme="success"
                            variant="ghost"
                            size="large"
                            width="116"
                            icon="s-play-fill"
                            :loading="isStarting"
                            @click="continueRecording"
                        >
                            继续
                        </abc-button>
                        <abc-button
                            v-show="state !== 'initial'"
                            key="stop"
                            class="stop-btn"
                            shape="round"
                            theme="danger"
                            variant="fill"
                            size="large"
                            width="116"
                            icon="s-stop-fill"
                            :loading="isStopping"
                            @click="stopRecording(true)"
                        >
                            结束
                        </abc-button>
                    </div>
                </transition>

                <abc-link
                    v-if="state === 'initial'"
                    size="small"
                    theme="default"
                    class="asr-tips-link"
                    @click="handleTipsClick"
                >
                    语音病历使用说明
                </abc-link>
                <abc-text
                    v-else
                    theme="gray-light"
                    size="mini"
                    style="height: 16px;"
                >
                    {{ remainTime < 60 ? `录音即将结束 ${formattedRemainTime}` : '' }}
                </abc-text>
            </abc-flex>
        </div>

        <voice-record-guide-modal
            v-if="showTipsModal"
            v-model="showTipsModal"
        >
        </voice-record-guide-modal>
    </div>
</template>

<script>
    import WaveVisualization from '../components/wave-visualization-v2.vue';
    import { useVoiceRecordStore } from '../hooks/use-voice-record';
    import { storeToRefs } from 'MfBase/pinia';
    import VoiceRecordGuideModal from './voice-record-guide-modal.vue';

    export default {
        name: 'VoiceRecordPanel',
        components: {
            VoiceRecordGuideModal,
            WaveVisualization,
        },
        props: {
            outpatientSheetId: {
                type: String,
                default: '',
            },
            patientInfo: {
                type: Object,
                required: false,
                default: null,
            },
        },
        setup() {
            // 使用 pinia store 获取所有响应式数据和方法
            const store = useVoiceRecordStore();

            const {
                state,
                isStarting,
                isStopping,
                currentSubtitleLines,
                isSubtitleScrolling,
                waveformData,
                formattedTime,
                remainTime,
                formattedRemainTime,
                recordTime,
                fullSentence,
            } = storeToRefs(store);

            const {
                startRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
                cleanup,
            } = store;

            return {
                state,
                isStarting,
                isStopping,
                currentSubtitleLines,
                isSubtitleScrolling,
                waveformData,
                recordTime,
                formattedTime,
                remainTime,
                formattedRemainTime,
                startRecording,
                pauseRecording,
                continueRecording,
                stopRecording,
                cleanup,
                fullSentence,
            };
        },
        data() {
            return {
                showTipsModal: false,
            };
        },
        watch: {
            remainTime(value) {
                if (value <= 0) {
                    console.log('RecordPanel timeout stop recording');
                    this.stopRecording(true);
                }
            },
        },
        beforeDestroy() {
            console.log('record-panel beforeDestroy');
        },
        methods: {
            handleStartBtnClick() {
                this.$abcPlatform.service.report.reportEventSLS('voice_record_start_btn_clk', '点击开始录音');
                this.startRecording(this.outpatientSheetId);
            },
            handleTipsClick() {
                this.showTipsModal = true;
            },
        },
    };
</script>

<style lang="scss">
@import "@/common/style/effect.scss";

.voice-record-panel {
    position: relative;
    height: 100%;

    .voice-record-main {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;

        .voice-record-title {
            font-size: 20px;
        }

        .audio-wave {
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 24px;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 146px;
            padding-bottom: var(--abc-paddingTB-xl);

            .wave-visualization {
                position: relative;
                width: 100%;
                height: 38px;
            }

            .subtitle-container {
                position: relative;
                width: 100%;
                overflow: hidden;
            }

            .subtitle-wrapper {
                position: relative;
                display: flex;
                justify-content: flex-end;
                width: 100%;
                height: 22px;
                overflow: hidden;
            }

            .wave-visualization::before,
            .subtitle-wrapper::before {
                position: absolute;
                top: 0;
                left: 0;
                z-index: 1;
                width: 40px;
                height: 100%;
                pointer-events: none;
                content: "";
                background: linear-gradient(270deg, rgba(238, 246, 255, 0) 0%, #eef6ff 100%);
            }

            .wave-visualization::after,
            .subtitle-wrapper::after {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 1;
                width: 40px;
                height: 100%;
                pointer-events: none;
                content: "";
                background: linear-gradient(270deg, #eef6ff 0%, rgba(238, 246, 255, 0) 100%);
            }

            .subtitle-content {
                overflow: visible;
                color: var(--abc-color-T2);
                text-align: right;
                white-space: nowrap;
            }
        }

        .start-button {
            position: absolute;
            height: 48px;
        }

        .abc-link--default.asr-tips-link {
            color: var(--abc-color-T3);

            &:not(.is-disabled):hover {
                color: var(--abc-color-T2);
            }
        }

        .record-controls {
            position: absolute;
            display: flex;
            gap: 16px;
            justify-content: center;
            width: 100%;

            .abc-button {
                height: 48px;
            }

            .abc-button + .abc-button {
                margin-left: 0;
            }
        }
    }
}

:root {
    --record-btn-animation-time: 0.3s;
}

/* 开始按钮收缩消失动画 */
.record-btn-fade-leave-active {
    transition: transform var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1), opacity var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1);
}

.record-btn-fade-leave-to {
    opacity: 0;
    transform: scale(0);
}

/* 暂停/继续/结束按钮入场动画 */
.record-btn-enter-enter-active,
.record-btn-enter-leave-active {
    transition: all var(--record-btn-animation-time) cubic-bezier(0.4, 0, 0.2, 1);
}

.record-btn-enter-enter {
    opacity: 0;
    transform: scale(0);
}

.record-btn-enter-enter-to {
    opacity: 1;
    transform: scale(1);
}
</style>
