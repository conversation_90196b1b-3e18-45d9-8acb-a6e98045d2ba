import WebRecorder from './webrecorder.js';
import SpeechRecognizer, {
    guid,
} from './speechrecognizer.js';
import { formatTime } from '@/common/utils';
import config from '../config.js';
import Logger from '../../../common/utils/logger';

const TAG = 'WebAudioSpeechRecognizer';

export default class WebAudioSpeechRecognizer {
    constructor(params, isLog, logger) {
        this.params = params;
        this.recorder = null;
        this.speechRecognizer = null;
        this.isCanSendData = false;
        this.isNormalEndStop = false;
        this.audioData = [];
        this.isLog = isLog;
        this.requestId = null;
        this.logger = logger;
    }
    async start() {
        return new Promise((resolve) => {
            try {
                this.isLog && console.log('start function is click');
                this.requestId = guid();
                this.recorder = new WebRecorder(this.requestId, this.params, this.isLog);
                this.recorder.OnReceivedData = (data) => {
                    if (this.isCanSendData) {
                        this.speechRecognizer && this.speechRecognizer.write(data);
                    }
                };
                this.recorder.onWaveformUpdate = (data) => {
                    this.onWaveformUpdate && this.onWaveformUpdate(data);
                };
                this.recorder.OnStart = async (audioTrack) => {
                    Logger.reportAnalytics('asr-context', {
                        mic: audioTrack?.label,
                        bizId: this.params.bizId,
                    });
                    // 录音开始，启动识别
                    await this._startRecognition();
                    resolve();
                };
                // 录音失败时
                this.recorder.OnError = (err) => {
                    this.speechRecognizer && this.speechRecognizer.close();
                    this.stop();
                    this.OnError(err);
                    resolve();
                };
                this.recorder.OnStop = async (res) => {
                    // console.log('WebAudioSpeechRecognizer.OnStop', this.speechRecognizer);
                    if (this.speechRecognizer) {
                        await this.speechRecognizer.stop();
                    // this.speechRecognizer = null;
                    }
                    this.OnRecorderStop(res);
                };
                this.recorder.start();
            } catch (e) {
                console.error(TAG, 'start exception', e);
            }
        });
    }
    async _startRecognition() {
        if (!this.speechRecognizer) {
            this.speechRecognizer = new SpeechRecognizer(this.params, this.requestId, this.isLog, this.logger);
        }
        // 开始识别
        this.speechRecognizer.OnRecognitionStart = (res) => {
            if (this.recorder) { // 录音正常
                this.OnRecognitionStart(res);
                this.isCanSendData = true;
            } else {
                this.speechRecognizer && this.speechRecognizer.close();
            }
        };
        // 一句话开始
        this.speechRecognizer.OnSentenceBegin = (res) => {
            this.OnSentenceBegin(res);
        };
        // 识别变化时
        this.speechRecognizer.OnRecognitionResultChange = (res) => {
            this.OnRecognitionResultChange(res);
        };
        // 一句话结束
        this.speechRecognizer.OnSentenceEnd = (res) => {
            this.OnSentenceEnd(res);
        };
        // 识别结束
        this.speechRecognizer.OnRecognitionComplete = (res) => {
            this.OnRecognitionComplete(res);
            this.isCanSendData = false;
            this.isNormalEndStop = true;
        };
        // 识别错误
        this.speechRecognizer.OnError = (res) => {
            this.isLog && console.log('this.speechRecognizer.onError');
            if (this.speechRecognizer && !this.isNormalEndStop) {
                this.OnError(res);
            }
            this.speechRecognizer?.stop();
            this.speechRecognizer = null;
            this.recorder && this.recorder.stop();
            this.isCanSendData = false;
        };
        if (config.debug) {
            this.mockSpeechRecognizer();
        } else {
            // 建立连接
            await this.speechRecognizer.start();
        }
    }
    mockSpeechRecognizer() {
        this.sentenceContent = '';
        this.mockStartTime = Date.now();
        this.sentenceDuration = 0;
        this.sentenceStartTime = 0;
        // 生成更真实的 mock 数据
        const mockPhrases = [
            '患者最近',
            '感冒发热',
            '头痛',
            '咳嗽',
            '有点',
            '喉咙痛',
            '食欲不振',
            '已经',
            '三天了',
            '。',

            '医生您好',
            '我前天',
            '感冒了',
            '现在',
            '还有一点',
            '咳嗽',
            '和',
            '喉咙痛',
            '其他的',
            '都好多了',
            '。',

            '我',
            '前天',
            '感冒了',
            '现在',
            '还有一点',
            '咳嗽',
            '和',
            '喉咙痛',
            '其他的',
            '都好多了',
            '。',

            '我',
            '前天',
            '感冒了',
            '现在',
            '还有一点',
            '咳嗽',
            '和',
            '喉咙痛',
            '其他的',
            '都好多了',
            '。',
        ];
        this.mockTimer = setInterval(() => {
            this.sentenceDuration += 300;
            const randomIndex = Math.floor(Math.random() * mockPhrases.length);

            // 模拟一句话开始
            if (this.sentenceContent === '') {
                this.sentenceDuration = 0;
                this.sentenceStartTime = Date.now() - this.mockStartTime;
                this.isLog && console.log('this.sentenceStartTime', this.sentenceStartTime, 'this.mockStartTime', this.mockStartTime);
                this.OnSentenceBegin({
                    start_time: this.sentenceStartTime,
                    end_time: this.sentenceStartTime + this.sentenceDuration,
                });
            }

            this.sentenceContent += `${mockPhrases[randomIndex]} `;
            this.OnRecognitionResultChange({
                result: {
                    voice_text_str: this.sentenceContent,
                    index: randomIndex,
                },
            });

            // 模拟一句话结束（当出现句号时）
            if (mockPhrases[randomIndex] === '。') {
                this.OnSentenceEnd({
                    result: {
                        start_time: this.sentenceStartTime,
                        end_time: this.sentenceStartTime + this.sentenceDuration,
                        time: formatTime(this.sentenceStartTime),
                        index: randomIndex,
                        voice_text_str: this.sentenceContent,
                    },
                });
                this.sentenceContent = '';
                this.sentenceDuration = 0;
            }
        }, 300);
    }
    async stop() {
        this.isLog && console.log('WebAudioSpeechRecognizer.stop');
        if (this.recorder) {
            await this.recorder.stop();
        }
        if (this.mockTimer) {
            clearInterval(this.mockTimer);
            this.OnSentenceEnd({
                result: {
                    start_time: this.sentenceStartTime,
                    end_time: this.sentenceStartTime + this.sentenceDuration,
                    voice_text_str: this.sentenceContent,
                },
            });
            // eslint-disable-next-line abc/no-timer-id
            setTimeout(() => {
                this.OnRecognitionComplete();
            }, 100);
            this.sentenceContent = '';
            this.mockTimer = null;
            this.mockStartTime = 0;
        }
    }
    destroyStream() {
        this.logger = null;
        this.isLog && console.log('destroyStream function is click', this.recorder);
        if (this.recorder) {
            this.recorder.destroyStream();
        }
    }
    // 开始识别的时候
    OnRecognitionStart(res) {
        this.isLog && console.log('OnRecognitionStart', res);
    }
    // 一句话开始的时候
    OnSentenceBegin(res) {
        this.isLog && console.log('OnSentenceBegin', res);
    }
    // 识别结果发生变化的时候
    OnRecognitionResultChange() {}
    // 一句话结束的时候
    OnSentenceEnd() {}
    // 识别结束的时候
    OnRecognitionComplete() {}
    // 识别失败
    OnError() {}
    OnRecorderStop() {}
    // 振幅变化时的回调
    onWaveformUpdate() {}
}
